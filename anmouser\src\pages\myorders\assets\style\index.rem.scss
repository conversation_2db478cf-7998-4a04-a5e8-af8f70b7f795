.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 10rem;
  height: 21.654rem;
  overflow: hidden;
  .box_1 {
    width: 10rem;
    height: 3.76rem;
    .group_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 0.854rem;
      .text_1 {
        width: 0.854rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.32rem;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 4rem;
        margin: 0.187rem 0 0 0.427rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 6.694rem;
      }
      .thumbnail_2 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 0.08rem;
      }
      .thumbnail_3 {
        width: 0.507rem;
        height: 0.507rem;
        margin: 0.187rem 0.4rem 0 0.08rem;
      }
    }
    .group_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 1.44rem;
      .thumbnail_4 {
        width: 0.24rem;
        height: 0.454rem;
        margin: 0.507rem 0 0 0.48rem;
      }
      .text_2 {
        width: 0.854rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.427rem 0 0 0.294rem;
      }
      .image_1 {
        width: 2.32rem;
        height: 0.854rem;
        margin: 0.294rem 0.16rem 0 5.654rem;
      }
    }
    .group_3 {
      background-color: rgba(255, 255, 255, 1);
      height: 1.174rem;
      margin-bottom: 0.294rem;
      width: 10rem;
      position: relative;
      .text-wrapper_1 {
        width: 8.934rem;
        height: 0.374rem;
        margin: 0.4rem 0 0 0.64rem;
        .text_3 {
          width: 0.747rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 0.373rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
        .text_4 {
          width: 1.12rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.373rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-left: 1.067rem;
        }
        .text_5 {
          width: 1.12rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.373rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-left: 0.88rem;
        }
        .text_6 {
          width: 1.12rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.373rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-left: 0.88rem;
        }
        .text_7 {
          width: 1.12rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.373rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-left: 0.88rem;
        }
      }
      .box_2 {
        position: absolute;
        left: 1.2rem;
        top: 0.16rem;
        width: 0.374rem;
        height: 0.374rem;
        .text-wrapper_2 {
          background-color: rgba(238, 12, 12, 1);
          border-radius: 50%;
          height: 0.374rem;
          width: 0.374rem;
          .text_8 {
            width: 0.16rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.266rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.054rem 0 0 0.107rem;
          }
        }
      }
    }
  }
  .box_3 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 5.44rem;
    width: 9.36rem;
    margin: -0.027rem 0 0 0.32rem;
    .text-wrapper_3 {
      width: 8.747rem;
      height: 0.347rem;
      margin: 0.48rem 0 0 0.32rem;
      .text_9 {
        width: 5.547rem;
        height: 0.347rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.346rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
      .text_10 {
        width: 0.96rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.027rem;
      }
    }
    .group_4 {
      width: 8.72rem;
      height: 2.107rem;
      margin: 0.96rem 0 0 0.294rem;
      .image-text_1 {
        width: 6.294rem;
        height: 2.107rem;
        .image_2 {
          width: 2.08rem;
          height: 2.08rem;
        }
        .text-group_1 {
          width: 4rem;
          height: 2.107rem;
          .text_11 {
            width: 2.4rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.4rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
          }
          .text_12 {
            width: 2.4rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.4rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: -0.374rem 0 0 0.054rem;
          }
          .text_13 {
            width: 1.814rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.267rem 0 0 0.054rem;
          }
          .text_14 {
            width: 3.947rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.187rem 0 0 0.054rem;
          }
          .text-wrapper_4 {
            width: 2.374rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.187rem 0 0 0.054rem;
            .text_15 {
              width: 2.374rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text_16 {
              width: 2.374rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text_17 {
              width: 2.374rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 0.4rem;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
          }
        }
      }
      .text_18 {
        width: 0.347rem;
        height: 0.374rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.373rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.027rem;
      }
    }
    .group_5 {
      width: 8.054rem;
      height: 0.667rem;
      margin: 0.454rem 0 0.427rem 1.04rem;
      .text-wrapper_5 {
        border-radius: 4px;
        height: 0.667rem;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 1.814rem;
        .text_19 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.587rem;
        }
      }
      .text-wrapper_6 {
        border-radius: 4px;
        height: 0.667rem;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 0.267rem;
        width: 1.814rem;
        .text_20 {
          width: 0.96rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.427rem;
        }
      }
      .text-wrapper_7 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 0.667rem;
        margin-left: 0.267rem;
        width: 1.814rem;
        .text_21 {
          width: 1.28rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.267rem;
        }
      }
      .text-wrapper_8 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 0.667rem;
        margin-left: 0.267rem;
        width: 1.814rem;
        .text_22 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.587rem;
        }
      }
    }
  }
  .box_4 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 5.44rem;
    width: 9.36rem;
    margin: 0.267rem 0 0 0.32rem;
    .text-wrapper_9 {
      width: 8.747rem;
      height: 0.347rem;
      margin: 0.48rem 0 0 0.32rem;
      .text_23 {
        width: 5.547rem;
        height: 0.347rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.346rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
      .text_24 {
        width: 0.96rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.027rem;
      }
    }
    .block_1 {
      width: 8.72rem;
      height: 2.107rem;
      margin: 0.96rem 0 0 0.294rem;
      .image-text_2 {
        width: 6.294rem;
        height: 2.107rem;
        .image_3 {
          width: 2.08rem;
          height: 2.08rem;
        }
        .text-group_2 {
          width: 4rem;
          height: 2.107rem;
          .text_25 {
            width: 2.4rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.4rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
          }
          .text_26 {
            width: 2.4rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.4rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: -0.374rem 0 0 0.054rem;
          }
          .text_27 {
            width: 1.814rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.267rem 0 0 0.054rem;
          }
          .text_28 {
            width: 3.947rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.187rem 0 0 0.054rem;
          }
          .text-wrapper_10 {
            width: 2.374rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.187rem 0 0 0.054rem;
            .text_29 {
              width: 2.374rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text_30 {
              width: 2.374rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text_31 {
              width: 2.374rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 0.4rem;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
          }
        }
      }
      .text_32 {
        width: 0.347rem;
        height: 0.374rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.373rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.027rem;
      }
    }
    .block_2 {
      width: 8.054rem;
      height: 0.667rem;
      margin: 0.454rem 0 0.427rem 1.04rem;
      .text-wrapper_11 {
        border-radius: 4px;
        height: 0.667rem;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 1.814rem;
        .text_33 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.587rem;
        }
      }
      .text-wrapper_12 {
        border-radius: 4px;
        height: 0.667rem;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 0.267rem;
        width: 1.814rem;
        .text_34 {
          width: 0.96rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.427rem;
        }
      }
      .text-wrapper_13 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 0.667rem;
        margin-left: 0.267rem;
        width: 1.814rem;
        .text_35 {
          width: 1.28rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.267rem;
        }
      }
      .text-wrapper_14 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 0.667rem;
        margin-left: 0.267rem;
        width: 1.814rem;
        .text_36 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.587rem;
        }
      }
    }
  }
  .box_5 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 5.44rem;
    width: 9.36rem;
    margin: 0.267rem 0 1.067rem 0.32rem;
    .text-wrapper_15 {
      width: 8.747rem;
      height: 0.347rem;
      margin: 0.48rem 0 0 0.32rem;
      .text_37 {
        width: 5.547rem;
        height: 0.347rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.346rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
      .text_38 {
        width: 0.96rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.027rem;
      }
    }
    .group_6 {
      width: 8.72rem;
      height: 2.107rem;
      margin: 0.96rem 0 0 0.294rem;
      .image-text_3 {
        width: 6.294rem;
        height: 2.107rem;
        .image_4 {
          width: 2.08rem;
          height: 2.08rem;
        }
        .text-group_3 {
          width: 4rem;
          height: 2.107rem;
          .text_39 {
            width: 2.4rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.4rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
          }
          .text_40 {
            width: 2.4rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.4rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: -0.374rem 0 0 0.054rem;
          }
          .text_41 {
            width: 1.814rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.267rem 0 0 0.054rem;
          }
          .text_42 {
            width: 3.947rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.187rem 0 0 0.054rem;
          }
          .text-wrapper_16 {
            width: 2.374rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.187rem 0 0 0.054rem;
            .text_43 {
              width: 2.374rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text_44 {
              width: 2.374rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text_45 {
              width: 2.374rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 0.4rem;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
            }
          }
        }
      }
      .text_46 {
        width: 0.347rem;
        height: 0.374rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.373rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.027rem;
      }
    }
    .group_7 {
      width: 8.054rem;
      height: 0.667rem;
      margin: 0.454rem 0 0.427rem 1.04rem;
      .text-wrapper_17 {
        border-radius: 4px;
        height: 0.667rem;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 1.814rem;
        .text_47 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.587rem;
        }
      }
      .text-wrapper_18 {
        border-radius: 4px;
        height: 0.667rem;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 0.267rem;
        width: 1.814rem;
        .text_48 {
          width: 0.96rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.427rem;
        }
      }
      .text-wrapper_19 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 0.667rem;
        margin-left: 0.267rem;
        width: 1.814rem;
        .text_49 {
          width: 1.28rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.267rem;
        }
      }
      .text-wrapper_20 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 0.667rem;
        margin-left: 0.267rem;
        width: 1.814rem;
        .text_50 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.187rem 0 0 0.587rem;
        }
      }
    }
  }
}
