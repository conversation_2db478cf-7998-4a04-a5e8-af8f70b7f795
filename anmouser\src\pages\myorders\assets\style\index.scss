.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 375px;
  height: 812px;
  overflow: hidden;
  .box_1 {
    width: 375px;
    height: 141px;
    .group_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 375px;
      height: 32px;
      .text_1 {
        width: 32px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 12px;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 150px;
        margin: 7px 0 0 16px;
      }
      .thumbnail_1 {
        width: 18px;
        height: 18px;
        margin: 7px 0 0 251px;
      }
      .thumbnail_2 {
        width: 18px;
        height: 18px;
        margin: 7px 0 0 3px;
      }
      .thumbnail_3 {
        width: 19px;
        height: 19px;
        margin: 7px 15px 0 3px;
      }
    }
    .group_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 375px;
      height: 54px;
      .thumbnail_4 {
        width: 9px;
        height: 17px;
        margin: 19px 0 0 18px;
      }
      .text_2 {
        width: 32px;
        height: 22px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 16px;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 22px;
        margin: 16px 0 0 11px;
      }
      .image_1 {
        width: 87px;
        height: 32px;
        margin: 11px 6px 0 212px;
      }
    }
    .group_3 {
      background-color: rgba(255, 255, 255, 1);
      height: 44px;
      margin-bottom: 11px;
      width: 375px;
      position: relative;
      .text-wrapper_1 {
        width: 335px;
        height: 14px;
        margin: 15px 0 0 24px;
        .text_3 {
          width: 28px;
          height: 14px;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 14px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
        }
        .text_4 {
          width: 42px;
          height: 14px;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 14px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-left: 40px;
        }
        .text_5 {
          width: 42px;
          height: 14px;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 14px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-left: 33px;
        }
        .text_6 {
          width: 42px;
          height: 14px;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 14px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-left: 33px;
        }
        .text_7 {
          width: 42px;
          height: 14px;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 14px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-left: 33px;
        }
      }
      .box_2 {
        position: absolute;
        left: 45px;
        top: 6px;
        width: 14px;
        height: 14px;
        .text-wrapper_2 {
          background-color: rgba(238, 12, 12, 1);
          border-radius: 50%;
          height: 14px;
          width: 14px;
          .text_8 {
            width: 6px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 10px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 2px 0 0 4px;
          }
        }
      }
    }
  }
  .box_3 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 204px;
    width: 351px;
    margin: -1px 0 0 12px;
    .text-wrapper_3 {
      width: 328px;
      height: 13px;
      margin: 18px 0 0 12px;
      .text_9 {
        width: 208px;
        height: 13px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 13px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
      .text_10 {
        width: 36px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 1px;
      }
    }
    .group_4 {
      width: 327px;
      height: 79px;
      margin: 36px 0 0 11px;
      .image-text_1 {
        width: 236px;
        height: 79px;
        .image_2 {
          width: 78px;
          height: 78px;
        }
        .text-group_1 {
          width: 150px;
          height: 79px;
          .text_11 {
            width: 90px;
            height: 15px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 15px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
          }
          .text_12 {
            width: 90px;
            height: 15px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 15px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: -14px 0 0 2px;
          }
          .text_13 {
            width: 68px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 10px 0 0 2px;
          }
          .text_14 {
            width: 148px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 7px 0 0 2px;
          }
          .text-wrapper_4 {
            width: 89px;
            height: 15px;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 7px 0 0 2px;
            .text_15 {
              width: 89px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
            }
            .text_16 {
              width: 89px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 12px;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
            }
            .text_17 {
              width: 89px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 15px;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
            }
          }
        }
      }
      .text_18 {
        width: 13px;
        height: 14px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 14px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 1px;
      }
    }
    .group_5 {
      width: 302px;
      height: 25px;
      margin: 17px 0 16px 39px;
      .text-wrapper_5 {
        border-radius: 4px;
        height: 25px;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 68px;
        .text_19 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 22px;
        }
      }
      .text-wrapper_6 {
        border-radius: 4px;
        height: 25px;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 10px;
        width: 68px;
        .text_20 {
          width: 36px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 16px;
        }
      }
      .text-wrapper_7 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 25px;
        margin-left: 10px;
        width: 68px;
        .text_21 {
          width: 48px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 10px;
        }
      }
      .text-wrapper_8 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 25px;
        margin-left: 10px;
        width: 68px;
        .text_22 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 22px;
        }
      }
    }
  }
  .box_4 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 204px;
    width: 351px;
    margin: 10px 0 0 12px;
    .text-wrapper_9 {
      width: 328px;
      height: 13px;
      margin: 18px 0 0 12px;
      .text_23 {
        width: 208px;
        height: 13px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 13px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
      .text_24 {
        width: 36px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 1px;
      }
    }
    .block_1 {
      width: 327px;
      height: 79px;
      margin: 36px 0 0 11px;
      .image-text_2 {
        width: 236px;
        height: 79px;
        .image_3 {
          width: 78px;
          height: 78px;
        }
        .text-group_2 {
          width: 150px;
          height: 79px;
          .text_25 {
            width: 90px;
            height: 15px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 15px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
          }
          .text_26 {
            width: 90px;
            height: 15px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 15px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: -14px 0 0 2px;
          }
          .text_27 {
            width: 68px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 10px 0 0 2px;
          }
          .text_28 {
            width: 148px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 7px 0 0 2px;
          }
          .text-wrapper_10 {
            width: 89px;
            height: 15px;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 7px 0 0 2px;
            .text_29 {
              width: 89px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
            }
            .text_30 {
              width: 89px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 12px;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
            }
            .text_31 {
              width: 89px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 15px;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
            }
          }
        }
      }
      .text_32 {
        width: 13px;
        height: 14px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 14px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 1px;
      }
    }
    .block_2 {
      width: 302px;
      height: 25px;
      margin: 17px 0 16px 39px;
      .text-wrapper_11 {
        border-radius: 4px;
        height: 25px;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 68px;
        .text_33 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 22px;
        }
      }
      .text-wrapper_12 {
        border-radius: 4px;
        height: 25px;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 10px;
        width: 68px;
        .text_34 {
          width: 36px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 16px;
        }
      }
      .text-wrapper_13 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 25px;
        margin-left: 10px;
        width: 68px;
        .text_35 {
          width: 48px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 10px;
        }
      }
      .text-wrapper_14 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 25px;
        margin-left: 10px;
        width: 68px;
        .text_36 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 22px;
        }
      }
    }
  }
  .box_5 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 204px;
    width: 351px;
    margin: 10px 0 40px 12px;
    .text-wrapper_15 {
      width: 328px;
      height: 13px;
      margin: 18px 0 0 12px;
      .text_37 {
        width: 208px;
        height: 13px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 13px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
      .text_38 {
        width: 36px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 1px;
      }
    }
    .group_6 {
      width: 327px;
      height: 79px;
      margin: 36px 0 0 11px;
      .image-text_3 {
        width: 236px;
        height: 79px;
        .image_4 {
          width: 78px;
          height: 78px;
        }
        .text-group_3 {
          width: 150px;
          height: 79px;
          .text_39 {
            width: 90px;
            height: 15px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 15px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
          }
          .text_40 {
            width: 90px;
            height: 15px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 15px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: -14px 0 0 2px;
          }
          .text_41 {
            width: 68px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 10px 0 0 2px;
          }
          .text_42 {
            width: 148px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 7px 0 0 2px;
          }
          .text-wrapper_16 {
            width: 89px;
            height: 15px;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 7px 0 0 2px;
            .text_43 {
              width: 89px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
            }
            .text_44 {
              width: 89px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 12px;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
            }
            .text_45 {
              width: 89px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 15px;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
            }
          }
        }
      }
      .text_46 {
        width: 13px;
        height: 14px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 14px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 1px;
      }
    }
    .group_7 {
      width: 302px;
      height: 25px;
      margin: 17px 0 16px 39px;
      .text-wrapper_17 {
        border-radius: 4px;
        height: 25px;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 68px;
        .text_47 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 22px;
        }
      }
      .text-wrapper_18 {
        border-radius: 4px;
        height: 25px;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 10px;
        width: 68px;
        .text_48 {
          width: 36px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 16px;
        }
      }
      .text-wrapper_19 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 25px;
        margin-left: 10px;
        width: 68px;
        .text_49 {
          width: 48px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 10px;
        }
      }
      .text-wrapper_20 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 25px;
        margin-left: 10px;
        width: 68px;
        .text_50 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 7px 0 0 22px;
        }
      }
    }
  }
}
