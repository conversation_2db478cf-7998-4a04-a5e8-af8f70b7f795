# 订单卡片样式修复说明

## 问题描述
循环生成的订单卡片中，除了第一个卡片有 `#f0f0f0` 背景，其他卡片都没有正确的背景样式。

## 问题原因
原始设计中每个订单卡片都是独立的容器（`.box_3`, `.box_4`, `.box_5`），每个都有自己的白色背景和圆角。但在循环实现中，所有订单都放在了一个 `.box_3` 容器内，导致只有容器有背景，而单个订单卡片没有。

## 解决方案

### 1. 修改 `.order-card` 样式
为每个循环生成的订单卡片添加独立的背景和样式：

```scss
.order-card {
  background-color: rgba(255, 255, 255, 1);  // 白色背景
  border-radius: 10px;                       // 圆角
  margin-bottom: 20rpx;                      // 卡片间距
  padding: 36rpx 24rpx 20rpx 24rpx;         // 内边距
  width: 702rpx;                            // 卡片宽度
  margin-left: 24rpx;                       // 左边距
  margin-right: 24rpx;                      // 右边距
  
  &:last-child {
    margin-bottom: 80rpx;                   // 最后一个卡片的底部边距
  }
}
```

### 2. 调整容器样式
将 `.box_3` 改为纯容器，移除背景和固定高度：

```scss
.box_3 {
  width: 750rpx;                           // 全宽容器
  margin: -2rpx 0 0 0;                     // 调整边距
}
```

### 3. 重置内部元素边距
由于卡片现在有了 `padding`，需要重置内部元素的 `margin`：

```scss
.order-card {
  .text-wrapper_3 {
    margin: 0 0 20rpx 0;                   // 订单头部边距
  }
  
  .group_4 {
    margin: 0 0 20rpx 0;                   // 订单详情边距
  }
  
  .group_5 {
    margin: 0;                             // 操作按钮边距
  }
}
```

## 修复效果

### 修复前：
- ❌ 只有第一个订单卡片有背景
- ❌ 其他订单卡片没有独立的视觉边界
- ❌ 卡片之间没有明显的分隔

### 修复后：
- ✅ 每个订单卡片都有独立的白色背景
- ✅ 每个卡片都有圆角边框
- ✅ 卡片之间有适当的间距
- ✅ 视觉效果与原设计一致

## 样式层级结构

```
.page (背景: #f6f6f6)
└── .box_3 (容器)
    └── .order-card (循环) (背景: #ffffff, 圆角: 10px)
        ├── .text-wrapper_3 (订单头部)
        ├── .group_4 (订单详情)
        └── .group_5 (操作按钮)
```

## 验证方法

1. **检查背景色**：每个订单卡片都应该有白色背景
2. **检查圆角**：每个卡片都应该有 10px 圆角
3. **检查间距**：卡片之间应该有 20rpx 间距
4. **检查最后一个卡片**：最后一个卡片底部应该有 80rpx 边距

## 注意事项

1. 如果需要调整卡片间距，修改 `.order-card` 的 `margin-bottom`
2. 如果需要调整卡片内边距，修改 `.order-card` 的 `padding`
3. 如果需要调整卡片宽度，同时修改 `width` 和 `margin-left/right`
4. 保持与原设计的视觉一致性

## 扩展建议

1. 可以添加卡片阴影效果：`box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1)`
2. 可以添加卡片悬停效果（如果支持）
3. 可以根据订单状态设置不同的卡片边框颜色
4. 可以添加卡片动画效果
