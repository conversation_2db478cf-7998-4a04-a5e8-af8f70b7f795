.page {
  background-color: rgba(248, 248, 248, 1);
  position: relative;
  width: 10rem;
  height: 21.654rem;
  overflow: hidden;
  .section_1 {
    background-color: rgba(0, 0, 0, 1);
    width: 10rem;
    height: 2.294rem;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 0.854rem;
      .text_1 {
        width: 0.854rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.32rem;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 4rem;
        margin: 0.187rem 0 0 0.427rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 6.694rem;
      }
      .thumbnail_2 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 0.08rem;
      }
      .thumbnail_3 {
        width: 0.507rem;
        height: 0.507rem;
        margin: 0.187rem 0.4rem 0 0.08rem;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 1.44rem;
      .text_2 {
        width: 1.707rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.427rem 0 0 0.32rem;
      }
      .image_1 {
        width: 2.32rem;
        height: 0.854rem;
        margin: 0.294rem 0.32rem 0 0;
      }
    }
  }
  .section_2 {
    position: relative;
    width: 10rem;
    height: 19.387rem;
    margin-bottom: 0.027rem;
    .group_1 {
      background-color: rgba(11, 206, 148, 1);
      width: 10rem;
      height: 4.32rem;
      .box_3 {
        width: 9.334rem;
        height: 0.614rem;
        margin: 0.4rem 0 0 0.347rem;
        .thumbnail_4 {
          width: 0.427rem;
          height: 0.454rem;
          margin-top: 0.027rem;
        }
        .image-text_1 {
          width: 5.627rem;
          height: 0.4rem;
          margin: 0.054rem 0 0 0.294rem;
          .text-group_1 {
            width: 4.96rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.4rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
          }
          .box_4 {
            background-color: rgba(255, 255, 255, 1);
            width: 0.427rem;
            height: 0.267rem;
            margin-top: 0.054rem;
          }
        }
        .block_1 {
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 2px 0px 2px 0px;
          width: 1.227rem;
          height: 0.614rem;
          margin-left: 0.534rem;
          .image-text_2 {
            width: 0.854rem;
            height: 0.24rem;
            margin: 0.187rem 0 0 0.214rem;
            .thumbnail_5 {
              width: 0.24rem;
              height: 0.24rem;
            }
            .text-group_2 {
              width: 0.534rem;
              height: 0.24rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.266rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 0.694rem;
            }
          }
        }
        .block_2 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 0px 2px 0px 2px;
          width: 1.227rem;
          height: 0.614rem;
          border: 1px solid rgba(224, 224, 224, 1);
          .image-text_3 {
            width: 0.827rem;
            height: 0.24rem;
            margin: 0.187rem 0 0 0.24rem;
            .thumbnail_6 {
              width: 0.24rem;
              height: 0.24rem;
            }
            .text-group_3 {
              width: 0.534rem;
              height: 0.24rem;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 0.266rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 0.694rem;
            }
          }
        }
      }
      .box_5 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 50px;
        width: 9.36rem;
        height: 1.014rem;
        margin: 0.294rem 0 0 0.32rem;
        .image-text_4 {
          width: 1.867rem;
          height: 0.347rem;
          margin: 0.347rem 0 0 0.454rem;
          .text-group_4 {
            width: 1.387rem;
            height: 0.347rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.346rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
          }
          .group_2 {
            background-color: rgba(2, 2, 2, 1);
            width: 0.267rem;
            height: 0.16rem;
            margin-top: 0.08rem;
          }
        }
        .text_3 {
          width: 3.12rem;
          height: 0.347rem;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.347rem 0 0 0.72rem;
        }
        .label_1 {
          width: 0.64rem;
          height: 0.64rem;
          margin: 0.187rem 0.294rem 0 2.267rem;
        }
      }
      .box_6 {
        width: 8.107rem;
        height: 0.614rem;
        margin: 0.374rem 0 0 0.4rem;
        .text-wrapper_1 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 100px;
          height: 0.614rem;
          width: 1.867rem;
          .text_4 {
            width: 1.387rem;
            height: 0.507rem;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 0.346rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 0.347rem;
            margin: 0.054rem 0 0 0.24rem;
          }
        }
        .text-wrapper_2 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 0.614rem;
          margin-left: 0.214rem;
          width: 1.867rem;
          .text_5 {
            width: 1.387rem;
            height: 0.507rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.346rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 0.347rem;
            margin: 0.054rem 0 0 0.24rem;
          }
        }
        .text-wrapper_3 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 0.614rem;
          margin-left: 0.214rem;
          width: 1.867rem;
          .text_6 {
            width: 1.387rem;
            height: 0.507rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.346rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 0.347rem;
            margin: 0.054rem 0 0 0.24rem;
          }
        }
        .text-wrapper_4 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 0.614rem;
          margin-left: 0.214rem;
          width: 1.867rem;
          .text_7 {
            width: 1.387rem;
            height: 0.507rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.346rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 0.347rem;
            margin: 0.054rem 0 0 0.24rem;
          }
        }
      }
      .box_7 {
        width: 9.334rem;
        height: 0.48rem;
        margin: 0.294rem 0 0.24rem 0.454rem;
        .text_8 {
          width: 1.28rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 0.32rem;
        }
        .group_3 {
          background-color: rgba(255, 255, 255, 1);
          width: 0.24rem;
          height: 0.16rem;
          margin: 0.32rem 0 0 0.294rem;
        }
        .text_9 {
          width: 1.28rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 0.32rem;
          margin-left: 0.694rem;
        }
        .group_4 {
          background-color: rgba(255, 255, 255, 1);
          width: 0.24rem;
          height: 0.16rem;
          margin: 0.32rem 0 0 0.294rem;
        }
        .text_10 {
          width: 1.28rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 0.32rem;
          margin-left: 0.694rem;
        }
        .group_5 {
          background-color: rgba(255, 255, 255, 1);
          width: 0.24rem;
          height: 0.16rem;
          margin: 0.32rem 0 0 0.294rem;
        }
        .text_11 {
          width: 1.28rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 0.32rem;
          margin-left: 0.694rem;
        }
        .group_6 {
          background-color: rgba(255, 255, 255, 1);
          width: 0.24rem;
          height: 0.16rem;
          margin: 0.32rem 0 0 0.294rem;
        }
      }
    }
    .group_7 {
      width: 9.36rem;
      height: 4.48rem;
      margin: 0.267rem 0 0 0.32rem;
      .group_8 {
        height: 4.48rem;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 4.534rem;
        .group_9 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 2.96rem;
          height: 0.56rem;
          margin: 3.627rem 0 0 0.667rem;
          .text-wrapper_5 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 0.56rem;
            width: 1.627rem;
            .text_12 {
              width: 1.307rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.107rem 0 0 0.107rem;
            }
          }
          .text_13 {
            width: 0.827rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.107rem 0.347rem 0 0.16rem;
          }
        }
      }
      .group_10 {
        height: 4.48rem;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 4.534rem;
        .box_8 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 2.96rem;
          height: 0.56rem;
          margin: 3.627rem 0 0 0.667rem;
          .text-wrapper_6 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 0.56rem;
            width: 1.627rem;
            .text_14 {
              width: 1.307rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.107rem 0 0 0.107rem;
            }
          }
          .text_15 {
            width: 0.827rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.107rem 0.347rem 0 0.16rem;
          }
        }
      }
    }
    .group_11 {
      width: 9.36rem;
      height: 3.814rem;
      margin-left: 0.32rem;
      .box_9 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 0px 0px 5px 5px;
        width: 4.534rem;
        height: 3.814rem;
        .box_10 {
          width: 2.694rem;
          height: 0.454rem;
          margin: 0.267rem 0 0 0.294rem;
          .text_16 {
            width: 1.227rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.4rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
          }
          .text-wrapper_7 {
            background-color: rgba(30, 30, 30, 1);
            border-radius: 50px;
            height: 0.427rem;
            margin-top: 0.027rem;
            width: 1.254rem;
            .text_17 {
              width: 0.88rem;
              height: 0.214rem;
              overflow-wrap: break-word;
              color: rgba(234, 225, 196, 1);
              font-size: 0.266rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.08rem 0 0 0.187rem;
            }
          }
        }
        .image-text_5 {
          width: 2.587rem;
          height: 0.32rem;
          margin: 0.214rem 0 0 0.294rem;
          .group_12 {
            width: 0.32rem;
            height: 0.294rem;
            background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG751b001b58a95440d7eae2a8c0959d9d.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-top: 0.027rem;
          }
          .text-group_5 {
            width: 2.187rem;
            height: 0.32rem;
            .text_18 {
              width: 0.214rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(247, 160, 68, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text_19 {
              width: 1.84rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
            }
          }
        }
        .image-text_6 {
          width: 1.947rem;
          height: 0.32rem;
          margin: 0.214rem 0 0 0.32rem;
          .thumbnail_7 {
            width: 0.24rem;
            height: 0.24rem;
            margin-top: 0.054rem;
          }
          .text-group_6 {
            width: 1.627rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
          }
        }
        .box_11 {
          width: 3.894rem;
          height: 0.72rem;
          margin: 0.294rem 0 0 0.347rem;
          .text-wrapper_8 {
            border-radius: 4px;
            height: 0.72rem;
            border: 1px solid rgba(11, 206, 148, 1);
            width: 1.787rem;
            .text_20 {
              width: 1.36rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.187rem 0 0 0.214rem;
            }
          }
          .text-wrapper_9 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 0.72rem;
            width: 1.787rem;
            .text_21 {
              width: 1.307rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.187rem 0 0 0.294rem;
            }
          }
        }
        .box_12 {
          width: 3.894rem;
          height: 0.374rem;
          margin: 0.454rem 0 0.187rem 0.347rem;
          .group_13 {
            background-color: rgba(150, 150, 150, 1);
            width: 0.427rem;
            height: 0.374rem;
          }
          .text_22 {
            width: 0.214rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.027rem 0 0 0.08rem;
          }
          .thumbnail_8 {
            width: 0.4rem;
            height: 0.374rem;
            margin-left: 0.667rem;
          }
          .text_23 {
            width: 0.214rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.027rem 0 0 0.08rem;
          }
          .image-text_7 {
            width: 1.12rem;
            height: 0.32rem;
            margin: 0.027rem 0 0 0.694rem;
            .group_14 {
              background-color: rgba(150, 150, 150, 1);
              width: 0.347rem;
              height: 0.32rem;
            }
            .text-group_7 {
              width: 0.667rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(150, 150, 150, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
            }
          }
        }
      }
      .box_13 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 0px 0px 5px 5px;
        width: 4.534rem;
        height: 3.814rem;
        .group_15 {
          width: 2.694rem;
          height: 0.454rem;
          margin: 0.267rem 0 0 0.294rem;
          .text_24 {
            width: 1.227rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.4rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
          }
          .text-wrapper_10 {
            background-color: rgba(30, 30, 30, 1);
            border-radius: 50px;
            height: 0.427rem;
            margin-top: 0.027rem;
            width: 1.254rem;
            .text_25 {
              width: 0.88rem;
              height: 0.214rem;
              overflow-wrap: break-word;
              color: rgba(234, 225, 196, 1);
              font-size: 0.266rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.08rem 0 0 0.187rem;
            }
          }
        }
        .image-text_8 {
          width: 2.587rem;
          height: 0.32rem;
          margin: 0.214rem 0 0 0.294rem;
          .section_3 {
            width: 0.32rem;
            height: 0.294rem;
            background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG751b001b58a95440d7eae2a8c0959d9d.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-top: 0.027rem;
          }
          .text-group_8 {
            width: 2.187rem;
            height: 0.32rem;
            .text_26 {
              width: 0.214rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(247, 160, 68, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text_27 {
              width: 1.84rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
            }
          }
        }
        .image-text_9 {
          width: 1.947rem;
          height: 0.32rem;
          margin: 0.214rem 0 0 0.32rem;
          .thumbnail_9 {
            width: 0.24rem;
            height: 0.24rem;
            margin-top: 0.054rem;
          }
          .text-group_9 {
            width: 1.627rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
          }
        }
        .group_16 {
          width: 3.894rem;
          height: 0.72rem;
          margin: 0.294rem 0 0 0.347rem;
          .text-wrapper_11 {
            border-radius: 4px;
            height: 0.72rem;
            border: 1px solid rgba(11, 206, 148, 1);
            width: 1.787rem;
            .text_28 {
              width: 1.36rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.187rem 0 0 0.214rem;
            }
          }
          .text-wrapper_12 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 0.72rem;
            width: 1.787rem;
            .text_29 {
              width: 1.307rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.187rem 0 0 0.294rem;
            }
          }
        }
        .group_17 {
          width: 3.894rem;
          height: 0.374rem;
          margin: 0.454rem 0 0.187rem 0.347rem;
          .box_14 {
            background-color: rgba(150, 150, 150, 1);
            width: 0.427rem;
            height: 0.374rem;
          }
          .text_30 {
            width: 0.214rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.027rem 0 0 0.08rem;
          }
          .box_15 {
            background-color: rgba(150, 150, 150, 1);
            width: 0.4rem;
            height: 0.374rem;
            margin-left: 0.667rem;
          }
          .text_31 {
            width: 0.214rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.027rem 0 0 0.08rem;
          }
          .image-text_10 {
            width: 1.12rem;
            height: 0.32rem;
            margin: 0.027rem 0 0 0.694rem;
            .section_4 {
              background-color: rgba(150, 150, 150, 1);
              width: 0.347rem;
              height: 0.32rem;
            }
            .text-group_10 {
              width: 0.667rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(150, 150, 150, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
            }
          }
        }
      }
    }
    .group_18 {
      width: 9.36rem;
      height: 4.48rem;
      margin: 0.267rem 0 0 0.32rem;
      .group_19 {
        width: 4.534rem;
        height: 4.48rem;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-wrapper_13 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 0.907rem;
          width: 2.507rem;
          margin: 0.907rem 0 0 1.494rem;
          .text_32 {
            width: 1.494rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.267rem 0 0 0.507rem;
          }
        }
        .block_3 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 2.96rem;
          height: 0.56rem;
          margin: 1.814rem 0 0.294rem 0.667rem;
          .text-wrapper_14 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 0.56rem;
            width: 1.627rem;
            .text_33 {
              width: 1.307rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.107rem 0 0 0.107rem;
            }
          }
          .text_34 {
            width: 0.827rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.107rem 0.347rem 0 0.16rem;
          }
        }
      }
      .group_20 {
        height: 4.48rem;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 4.534rem;
        .box_16 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 2.96rem;
          height: 0.56rem;
          margin: 3.627rem 0 0 0.667rem;
          .text-wrapper_15 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 0.56rem;
            width: 1.627rem;
            .text_35 {
              width: 1.307rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.107rem 0 0 0.107rem;
            }
          }
          .text_36 {
            width: 0.827rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.107rem 0.347rem 0 0.16rem;
          }
        }
      }
    }
    .text-wrapper_16 {
      width: 3.147rem;
      height: 0.32rem;
      margin: 9.094rem 0 7.654rem 5.92rem;
      .text_37 {
        width: 0.64rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
      .text_38 {
        width: 0.64rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
    }
    .group_21 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 5px 5px;
      position: absolute;
      left: 0.32rem;
      top: 17.627rem;
      width: 4.534rem;
      height: 3.814rem;
      .box_17 {
        width: 2.694rem;
        height: 0.454rem;
        margin: 0.267rem 0 0 0.294rem;
        .text_39 {
          width: 1.227rem;
          height: 0.4rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.4rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
        }
        .text-wrapper_17 {
          background-color: rgba(30, 30, 30, 1);
          border-radius: 50px;
          height: 0.427rem;
          margin-top: 0.027rem;
          width: 1.254rem;
          .text_40 {
            width: 0.88rem;
            height: 0.214rem;
            overflow-wrap: break-word;
            color: rgba(234, 225, 196, 1);
            font-size: 0.266rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.08rem 0 0 0.187rem;
          }
        }
      }
      .image-text_11 {
        width: 1.947rem;
        height: 0.32rem;
        margin: 0.747rem 0 0 0.32rem;
        .thumbnail_10 {
          width: 0.24rem;
          height: 0.24rem;
          margin-top: 0.054rem;
        }
        .text-group_11 {
          width: 1.627rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
        }
      }
      .box_18 {
        width: 3.894rem;
        height: 0.72rem;
        margin: 0.294rem 0 0 0.347rem;
        .text-wrapper_18 {
          border-radius: 4px;
          height: 0.72rem;
          border: 1px solid rgba(11, 206, 148, 1);
          width: 1.787rem;
          .text_41 {
            width: 1.36rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.187rem 0 0 0.214rem;
          }
        }
        .text-wrapper_19 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 0.72rem;
          width: 1.787rem;
          .text_42 {
            width: 1.307rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.187rem 0 0 0.294rem;
          }
        }
      }
      .box_19 {
        width: 3.894rem;
        height: 0.374rem;
        margin: 0.454rem 0 0.187rem 0.347rem;
        .block_4 {
          background-color: rgba(150, 150, 150, 1);
          width: 0.427rem;
          height: 0.374rem;
        }
        .text_43 {
          width: 0.214rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.027rem 0 0 0.08rem;
        }
        .block_5 {
          background-color: rgba(150, 150, 150, 1);
          width: 0.4rem;
          height: 0.374rem;
          margin-left: 0.667rem;
        }
        .text_44 {
          width: 0.214rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.027rem 0 0 0.08rem;
        }
        .image-text_12 {
          width: 1.12rem;
          height: 0.32rem;
          margin: 0.027rem 0 0 0.694rem;
          .box_20 {
            background-color: rgba(150, 150, 150, 1);
            width: 0.347rem;
            height: 0.32rem;
          }
          .text-group_12 {
            width: 0.667rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
          }
        }
      }
    }
    .group_22 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 5px 5px;
      position: absolute;
      left: 5.147rem;
      top: 17.627rem;
      width: 4.534rem;
      height: 3.814rem;
      .group_23 {
        width: 2.694rem;
        height: 0.454rem;
        margin: 0.267rem 0 0 0.294rem;
        .text_45 {
          width: 1.227rem;
          height: 0.4rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.4rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
        }
        .text-wrapper_20 {
          background-color: rgba(30, 30, 30, 1);
          border-radius: 50px;
          height: 0.427rem;
          margin-top: 0.027rem;
          width: 1.254rem;
          .text_46 {
            width: 0.88rem;
            height: 0.214rem;
            overflow-wrap: break-word;
            color: rgba(234, 225, 196, 1);
            font-size: 0.266rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.08rem 0 0 0.187rem;
          }
        }
      }
      .image-text_13 {
        width: 1.947rem;
        height: 0.32rem;
        margin: 0.747rem 0 0 0.32rem;
        .thumbnail_11 {
          width: 0.24rem;
          height: 0.24rem;
          margin-top: 0.054rem;
        }
        .text-group_13 {
          width: 1.627rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
        }
      }
      .group_24 {
        width: 3.894rem;
        height: 0.72rem;
        margin: 0.294rem 0 0 0.347rem;
        .text-wrapper_21 {
          border-radius: 4px;
          height: 0.72rem;
          border: 1px solid rgba(11, 206, 148, 1);
          width: 1.787rem;
          .text_47 {
            width: 1.36rem;
            height: 0.4rem;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.187rem 0 0 0.214rem;
          }
        }
        .text-wrapper_22 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 0.72rem;
          width: 1.787rem;
          .text_48 {
            width: 1.307rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.187rem 0 0 0.294rem;
          }
        }
      }
      .group_25 {
        width: 3.894rem;
        height: 0.374rem;
        margin: 0.454rem 0 0.187rem 0.347rem;
        .box_21 {
          background-color: rgba(150, 150, 150, 1);
          width: 0.427rem;
          height: 0.374rem;
        }
        .text_49 {
          width: 0.214rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.027rem 0 0 0.08rem;
        }
        .box_22 {
          background-color: rgba(150, 150, 150, 1);
          width: 0.4rem;
          height: 0.374rem;
          margin-left: 0.667rem;
        }
        .text_50 {
          width: 0.214rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.027rem 0 0 0.08rem;
        }
        .image-text_14 {
          width: 1.12rem;
          height: 0.32rem;
          margin: 0.027rem 0 0 0.694rem;
          .group_26 {
            background-color: rgba(150, 150, 150, 1);
            width: 0.347rem;
            height: 0.32rem;
          }
          .text-group_14 {
            width: 0.667rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
          }
        }
      }
    }
    .text-wrapper_23 {
      position: absolute;
      left: -10.533rem;
      top: 23.574rem;
      width: 4.48rem;
      height: 4.48rem;
      background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNGa911c964ed32937659ea639dfb0c05a2.png)
        100% no-repeat;
      background-size: 100% 100%;
      .text_51 {
        width: 0.64rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 3.147rem 0 0 11.467rem;
      }
      .text_52 {
        width: 0.64rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 3.147rem -10.107rem 0 1.84rem;
      }
    }
    .text-wrapper_24 {
      background-color: rgba(16, 16, 16, 0.1);
      border-radius: 100px;
      height: 0.614rem;
      width: 1.867rem;
      position: absolute;
      left: 8.747rem;
      top: 2.694rem;
      .text_53 {
        width: 1.387rem;
        height: 0.507rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.346rem;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.347rem;
        margin: 0.054rem 0 0 0.24rem;
      }
    }
    .group_27 {
      background-color: rgba(255, 255, 255, 1);
      position: absolute;
      left: 0;
      top: 18.054rem;
      width: 10rem;
      height: 1.307rem;
      .image-text_15 {
        width: 0.64rem;
        height: 1.04rem;
        margin-top: 0.16rem;
        .label_2 {
          width: 0.587rem;
          height: 0.587rem;
          margin-left: 0.027rem;
        }
        .text-group_15 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.134rem;
        }
      }
      .image-text_16 {
        width: 0.64rem;
        height: 1.014rem;
        margin-top: 0.187rem;
        .block_6 {
          height: 0.587rem;
          background: url(/static/lanhu_shouyejishikapian/a1163f772a4e46fcade03d5393284bde_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 0.027rem;
          width: 0.587rem;
          .group_28 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 50%;
            width: 0.187rem;
            height: 0.187rem;
            margin: 0.08rem 0 0 0.267rem;
          }
        }
        .text-group_16 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.107rem;
        }
      }
      .image-text_17 {
        width: 0.64rem;
        height: 1.014rem;
        margin-top: 0.187rem;
        .label_3 {
          width: 0.587rem;
          height: 0.587rem;
          margin-left: 0.027rem;
        }
        .text-group_17 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.107rem;
        }
      }
      .image-text_18 {
        width: 0.64rem;
        height: 1.014rem;
        margin-top: 0.187rem;
        .label_4 {
          width: 0.587rem;
          height: 0.587rem;
          margin-left: 0.027rem;
        }
        .text-group_18 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.107rem;
        }
      }
    }
    .text-wrapper_25 {
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 5px;
      height: 0.907rem;
      width: 2.507rem;
      position: absolute;
      left: 1.84rem;
      top: 12.96rem;
      .text_54 {
        width: 1.494rem;
        height: 0.374rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.373rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 0.267rem 0 0 0.507rem;
      }
    }
  }
}
