.page {
  background-color: rgba(248, 248, 248, 1);
  position: relative;
  width: 100vw;
  height: 216.54vw;
  overflow: hidden;
  .section_1 {
    background-color: rgba(0, 0, 0, 1);
    width: 100vw;
    height: 22.94vw;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 8.54vw;
      .text_1 {
        width: 8.54vw;
        height: 4.8vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 3.2vw;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 40vw;
        margin: 1.86vw 0 0 4.26vw;
      }
      .thumbnail_1 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 66.93vw;
      }
      .thumbnail_2 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 0.8vw;
      }
      .thumbnail_3 {
        width: 5.07vw;
        height: 5.07vw;
        margin: 1.86vw 4vw 0 0.8vw;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 14.4vw;
      .text_2 {
        width: 17.07vw;
        height: 5.87vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 4.26vw;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 5.87vw;
        margin: 4.26vw 0 0 3.2vw;
      }
      .image_1 {
        width: 23.2vw;
        height: 8.54vw;
        margin: 2.93vw 3.2vw 0 0;
      }
    }
  }
  .section_2 {
    position: relative;
    width: 100vw;
    height: 193.87vw;
    margin-bottom: 0.27vw;
    .group_1 {
      background-color: rgba(11, 206, 148, 1);
      width: 100vw;
      height: 43.2vw;
      .box_3 {
        width: 93.34vw;
        height: 6.14vw;
        margin: 4vw 0 0 3.46vw;
        .thumbnail_4 {
          width: 4.27vw;
          height: 4.54vw;
          margin-top: 0.27vw;
        }
        .image-text_1 {
          width: 56.27vw;
          height: 4vw;
          margin: 0.53vw 0 0 2.93vw;
          .text-group_1 {
            width: 49.6vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 4vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
          }
          .box_4 {
            background-color: rgba(255, 255, 255, 1);
            width: 4.27vw;
            height: 2.67vw;
            margin-top: 0.54vw;
          }
        }
        .block_1 {
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 2px 0px 2px 0px;
          width: 12.27vw;
          height: 6.14vw;
          margin-left: 5.34vw;
          .image-text_2 {
            width: 8.54vw;
            height: 2.4vw;
            margin: 1.86vw 0 0 2.13vw;
            .thumbnail_5 {
              width: 2.4vw;
              height: 2.4vw;
            }
            .text-group_2 {
              width: 5.34vw;
              height: 2.4vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 2.66vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 6.94vw;
            }
          }
        }
        .block_2 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 0px 2px 0px 2px;
          width: 12.27vw;
          height: 6.14vw;
          border: 1px solid rgba(224, 224, 224, 1);
          .image-text_3 {
            width: 8.27vw;
            height: 2.4vw;
            margin: 1.86vw 0 0 2.4vw;
            .thumbnail_6 {
              width: 2.4vw;
              height: 2.4vw;
            }
            .text-group_3 {
              width: 5.34vw;
              height: 2.4vw;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 2.66vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 6.94vw;
            }
          }
        }
      }
      .box_5 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 50px;
        width: 93.6vw;
        height: 10.14vw;
        margin: 2.93vw 0 0 3.2vw;
        .image-text_4 {
          width: 18.67vw;
          height: 3.47vw;
          margin: 3.46vw 0 0 4.53vw;
          .text-group_4 {
            width: 13.87vw;
            height: 3.47vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 3.46vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
          }
          .group_2 {
            background-color: rgba(2, 2, 2, 1);
            width: 2.67vw;
            height: 1.6vw;
            margin-top: 0.8vw;
          }
        }
        .text_3 {
          width: 31.2vw;
          height: 3.47vw;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 3.46vw 0 0 7.2vw;
        }
        .label_1 {
          width: 6.4vw;
          height: 6.4vw;
          margin: 1.86vw 2.93vw 0 22.66vw;
        }
      }
      .box_6 {
        width: 81.07vw;
        height: 6.14vw;
        margin: 3.73vw 0 0 4vw;
        .text-wrapper_1 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 100px;
          height: 6.14vw;
          width: 18.67vw;
          .text_4 {
            width: 13.87vw;
            height: 5.07vw;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 3.46vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 3.47vw;
            margin: 0.53vw 0 0 2.4vw;
          }
        }
        .text-wrapper_2 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 6.14vw;
          margin-left: 2.14vw;
          width: 18.67vw;
          .text_5 {
            width: 13.87vw;
            height: 5.07vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.46vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 3.47vw;
            margin: 0.53vw 0 0 2.4vw;
          }
        }
        .text-wrapper_3 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 6.14vw;
          margin-left: 2.14vw;
          width: 18.67vw;
          .text_6 {
            width: 13.87vw;
            height: 5.07vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.46vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 3.47vw;
            margin: 0.53vw 0 0 2.4vw;
          }
        }
        .text-wrapper_4 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 6.14vw;
          margin-left: 2.14vw;
          width: 18.67vw;
          .text_7 {
            width: 13.87vw;
            height: 5.07vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.46vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 3.47vw;
            margin: 0.53vw 0 0 2.4vw;
          }
        }
      }
      .box_7 {
        width: 93.34vw;
        height: 4.8vw;
        margin: 2.93vw 0 2.4vw 4.53vw;
        .text_8 {
          width: 12.8vw;
          height: 4.8vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 3.2vw;
        }
        .group_3 {
          background-color: rgba(255, 255, 255, 1);
          width: 2.4vw;
          height: 1.6vw;
          margin: 3.2vw 0 0 2.93vw;
        }
        .text_9 {
          width: 12.8vw;
          height: 4.8vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 3.2vw;
          margin-left: 6.94vw;
        }
        .group_4 {
          background-color: rgba(255, 255, 255, 1);
          width: 2.4vw;
          height: 1.6vw;
          margin: 3.2vw 0 0 2.93vw;
        }
        .text_10 {
          width: 12.8vw;
          height: 4.8vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 3.2vw;
          margin-left: 6.94vw;
        }
        .group_5 {
          background-color: rgba(255, 255, 255, 1);
          width: 2.4vw;
          height: 1.6vw;
          margin: 3.2vw 0 0 2.93vw;
        }
        .text_11 {
          width: 12.8vw;
          height: 4.8vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 3.2vw;
          margin-left: 6.94vw;
        }
        .group_6 {
          background-color: rgba(255, 255, 255, 1);
          width: 2.4vw;
          height: 1.6vw;
          margin: 3.2vw 0 0 2.93vw;
        }
      }
    }
    .group_7 {
      width: 93.6vw;
      height: 44.8vw;
      margin: 2.66vw 0 0 3.2vw;
      .group_8 {
        height: 44.8vw;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 45.34vw;
        .group_9 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 29.6vw;
          height: 5.6vw;
          margin: 36.26vw 0 0 6.66vw;
          .text-wrapper_5 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 5.6vw;
            width: 16.27vw;
            .text_12 {
              width: 13.07vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 1.06vw 0 0 1.06vw;
            }
          }
          .text_13 {
            width: 8.27vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.06vw 3.46vw 0 1.6vw;
          }
        }
      }
      .group_10 {
        height: 44.8vw;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 45.34vw;
        .box_8 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 29.6vw;
          height: 5.6vw;
          margin: 36.26vw 0 0 6.66vw;
          .text-wrapper_6 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 5.6vw;
            width: 16.27vw;
            .text_14 {
              width: 13.07vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 1.06vw 0 0 1.06vw;
            }
          }
          .text_15 {
            width: 8.27vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.06vw 3.46vw 0 1.6vw;
          }
        }
      }
    }
    .group_11 {
      width: 93.6vw;
      height: 38.14vw;
      margin-left: 3.2vw;
      .box_9 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 0px 0px 5px 5px;
        width: 45.34vw;
        height: 38.14vw;
        .box_10 {
          width: 26.94vw;
          height: 4.54vw;
          margin: 2.66vw 0 0 2.93vw;
          .text_16 {
            width: 12.27vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 4vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
          }
          .text-wrapper_7 {
            background-color: rgba(30, 30, 30, 1);
            border-radius: 50px;
            height: 4.27vw;
            margin-top: 0.27vw;
            width: 12.54vw;
            .text_17 {
              width: 8.8vw;
              height: 2.14vw;
              overflow-wrap: break-word;
              color: rgba(234, 225, 196, 1);
              font-size: 2.66vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 0.8vw 0 0 1.86vw;
            }
          }
        }
        .image-text_5 {
          width: 25.87vw;
          height: 3.2vw;
          margin: 2.13vw 0 0 2.93vw;
          .group_12 {
            width: 3.2vw;
            height: 2.94vw;
            background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG751b001b58a95440d7eae2a8c0959d9d.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-top: 0.27vw;
          }
          .text-group_5 {
            width: 21.87vw;
            height: 3.2vw;
            .text_18 {
              width: 2.14vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(247, 160, 68, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text_19 {
              width: 18.4vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
            }
          }
        }
        .image-text_6 {
          width: 19.47vw;
          height: 3.2vw;
          margin: 2.13vw 0 0 3.2vw;
          .thumbnail_7 {
            width: 2.4vw;
            height: 2.4vw;
            margin-top: 0.54vw;
          }
          .text-group_6 {
            width: 16.27vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
          }
        }
        .box_11 {
          width: 38.94vw;
          height: 7.2vw;
          margin: 2.93vw 0 0 3.46vw;
          .text-wrapper_8 {
            border-radius: 4px;
            height: 7.2vw;
            border: 1px solid rgba(11, 206, 148, 1);
            width: 17.87vw;
            .text_20 {
              width: 13.6vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 1.86vw 0 0 2.13vw;
            }
          }
          .text-wrapper_9 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 7.2vw;
            width: 17.87vw;
            .text_21 {
              width: 13.07vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 1.86vw 0 0 2.93vw;
            }
          }
        }
        .box_12 {
          width: 38.94vw;
          height: 3.74vw;
          margin: 4.53vw 0 1.86vw 3.46vw;
          .group_13 {
            background-color: rgba(150, 150, 150, 1);
            width: 4.27vw;
            height: 3.74vw;
          }
          .text_22 {
            width: 2.14vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 0.26vw 0 0 0.8vw;
          }
          .thumbnail_8 {
            width: 4vw;
            height: 3.74vw;
            margin-left: 6.67vw;
          }
          .text_23 {
            width: 2.14vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 0.26vw 0 0 0.8vw;
          }
          .image-text_7 {
            width: 11.2vw;
            height: 3.2vw;
            margin: 0.26vw 0 0 6.93vw;
            .group_14 {
              background-color: rgba(150, 150, 150, 1);
              width: 3.47vw;
              height: 3.2vw;
            }
            .text-group_7 {
              width: 6.67vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(150, 150, 150, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
            }
          }
        }
      }
      .box_13 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 0px 0px 5px 5px;
        width: 45.34vw;
        height: 38.14vw;
        .group_15 {
          width: 26.94vw;
          height: 4.54vw;
          margin: 2.66vw 0 0 2.93vw;
          .text_24 {
            width: 12.27vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 4vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
          }
          .text-wrapper_10 {
            background-color: rgba(30, 30, 30, 1);
            border-radius: 50px;
            height: 4.27vw;
            margin-top: 0.27vw;
            width: 12.54vw;
            .text_25 {
              width: 8.8vw;
              height: 2.14vw;
              overflow-wrap: break-word;
              color: rgba(234, 225, 196, 1);
              font-size: 2.66vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 0.8vw 0 0 1.86vw;
            }
          }
        }
        .image-text_8 {
          width: 25.87vw;
          height: 3.2vw;
          margin: 2.13vw 0 0 2.93vw;
          .section_3 {
            width: 3.2vw;
            height: 2.94vw;
            background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG751b001b58a95440d7eae2a8c0959d9d.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-top: 0.27vw;
          }
          .text-group_8 {
            width: 21.87vw;
            height: 3.2vw;
            .text_26 {
              width: 2.14vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(247, 160, 68, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text_27 {
              width: 18.4vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
            }
          }
        }
        .image-text_9 {
          width: 19.47vw;
          height: 3.2vw;
          margin: 2.13vw 0 0 3.2vw;
          .thumbnail_9 {
            width: 2.4vw;
            height: 2.4vw;
            margin-top: 0.54vw;
          }
          .text-group_9 {
            width: 16.27vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
          }
        }
        .group_16 {
          width: 38.94vw;
          height: 7.2vw;
          margin: 2.93vw 0 0 3.46vw;
          .text-wrapper_11 {
            border-radius: 4px;
            height: 7.2vw;
            border: 1px solid rgba(11, 206, 148, 1);
            width: 17.87vw;
            .text_28 {
              width: 13.6vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 1.86vw 0 0 2.13vw;
            }
          }
          .text-wrapper_12 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 7.2vw;
            width: 17.87vw;
            .text_29 {
              width: 13.07vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 1.86vw 0 0 2.93vw;
            }
          }
        }
        .group_17 {
          width: 38.94vw;
          height: 3.74vw;
          margin: 4.53vw 0 1.86vw 3.46vw;
          .box_14 {
            background-color: rgba(150, 150, 150, 1);
            width: 4.27vw;
            height: 3.74vw;
          }
          .text_30 {
            width: 2.14vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 0.26vw 0 0 0.8vw;
          }
          .box_15 {
            background-color: rgba(150, 150, 150, 1);
            width: 4vw;
            height: 3.74vw;
            margin-left: 6.67vw;
          }
          .text_31 {
            width: 2.14vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 0.26vw 0 0 0.8vw;
          }
          .image-text_10 {
            width: 11.2vw;
            height: 3.2vw;
            margin: 0.26vw 0 0 6.93vw;
            .section_4 {
              background-color: rgba(150, 150, 150, 1);
              width: 3.47vw;
              height: 3.2vw;
            }
            .text-group_10 {
              width: 6.67vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(150, 150, 150, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
            }
          }
        }
      }
    }
    .group_18 {
      width: 93.6vw;
      height: 44.8vw;
      margin: 2.66vw 0 0 3.2vw;
      .group_19 {
        width: 45.34vw;
        height: 44.8vw;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-wrapper_13 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 9.07vw;
          width: 25.07vw;
          margin: 9.06vw 0 0 14.93vw;
          .text_32 {
            width: 14.94vw;
            height: 3.74vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 2.66vw 0 0 5.06vw;
          }
        }
        .block_3 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 29.6vw;
          height: 5.6vw;
          margin: 18.13vw 0 2.93vw 6.66vw;
          .text-wrapper_14 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 5.6vw;
            width: 16.27vw;
            .text_33 {
              width: 13.07vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 1.06vw 0 0 1.06vw;
            }
          }
          .text_34 {
            width: 8.27vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.06vw 3.46vw 0 1.6vw;
          }
        }
      }
      .group_20 {
        height: 44.8vw;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 45.34vw;
        .box_16 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 29.6vw;
          height: 5.6vw;
          margin: 36.26vw 0 0 6.66vw;
          .text-wrapper_15 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 5.6vw;
            width: 16.27vw;
            .text_35 {
              width: 13.07vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 1.06vw 0 0 1.06vw;
            }
          }
          .text_36 {
            width: 8.27vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.06vw 3.46vw 0 1.6vw;
          }
        }
      }
    }
    .text-wrapper_16 {
      width: 31.47vw;
      height: 3.2vw;
      margin: 90.93vw 0 76.53vw 59.2vw;
      .text_37 {
        width: 6.4vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
      .text_38 {
        width: 6.4vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
    }
    .group_21 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 5px 5px;
      position: absolute;
      left: 3.2vw;
      top: 176.27vw;
      width: 45.34vw;
      height: 38.14vw;
      .box_17 {
        width: 26.94vw;
        height: 4.54vw;
        margin: 2.66vw 0 0 2.93vw;
        .text_39 {
          width: 12.27vw;
          height: 4vw;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 4vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
        }
        .text-wrapper_17 {
          background-color: rgba(30, 30, 30, 1);
          border-radius: 50px;
          height: 4.27vw;
          margin-top: 0.27vw;
          width: 12.54vw;
          .text_40 {
            width: 8.8vw;
            height: 2.14vw;
            overflow-wrap: break-word;
            color: rgba(234, 225, 196, 1);
            font-size: 2.66vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 0.8vw 0 0 1.86vw;
          }
        }
      }
      .image-text_11 {
        width: 19.47vw;
        height: 3.2vw;
        margin: 7.46vw 0 0 3.2vw;
        .thumbnail_10 {
          width: 2.4vw;
          height: 2.4vw;
          margin-top: 0.54vw;
        }
        .text-group_11 {
          width: 16.27vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
        }
      }
      .box_18 {
        width: 38.94vw;
        height: 7.2vw;
        margin: 2.93vw 0 0 3.46vw;
        .text-wrapper_18 {
          border-radius: 4px;
          height: 7.2vw;
          border: 1px solid rgba(11, 206, 148, 1);
          width: 17.87vw;
          .text_41 {
            width: 13.6vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.86vw 0 0 2.13vw;
          }
        }
        .text-wrapper_19 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 7.2vw;
          width: 17.87vw;
          .text_42 {
            width: 13.07vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.86vw 0 0 2.93vw;
          }
        }
      }
      .box_19 {
        width: 38.94vw;
        height: 3.74vw;
        margin: 4.53vw 0 1.86vw 3.46vw;
        .block_4 {
          background-color: rgba(150, 150, 150, 1);
          width: 4.27vw;
          height: 3.74vw;
        }
        .text_43 {
          width: 2.14vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 0.26vw 0 0 0.8vw;
        }
        .block_5 {
          background-color: rgba(150, 150, 150, 1);
          width: 4vw;
          height: 3.74vw;
          margin-left: 6.67vw;
        }
        .text_44 {
          width: 2.14vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 0.26vw 0 0 0.8vw;
        }
        .image-text_12 {
          width: 11.2vw;
          height: 3.2vw;
          margin: 0.26vw 0 0 6.93vw;
          .box_20 {
            background-color: rgba(150, 150, 150, 1);
            width: 3.47vw;
            height: 3.2vw;
          }
          .text-group_12 {
            width: 6.67vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
          }
        }
      }
    }
    .group_22 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 5px 5px;
      position: absolute;
      left: 51.47vw;
      top: 176.27vw;
      width: 45.34vw;
      height: 38.14vw;
      .group_23 {
        width: 26.94vw;
        height: 4.54vw;
        margin: 2.66vw 0 0 2.93vw;
        .text_45 {
          width: 12.27vw;
          height: 4vw;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 4vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
        }
        .text-wrapper_20 {
          background-color: rgba(30, 30, 30, 1);
          border-radius: 50px;
          height: 4.27vw;
          margin-top: 0.27vw;
          width: 12.54vw;
          .text_46 {
            width: 8.8vw;
            height: 2.14vw;
            overflow-wrap: break-word;
            color: rgba(234, 225, 196, 1);
            font-size: 2.66vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 0.8vw 0 0 1.86vw;
          }
        }
      }
      .image-text_13 {
        width: 19.47vw;
        height: 3.2vw;
        margin: 7.46vw 0 0 3.2vw;
        .thumbnail_11 {
          width: 2.4vw;
          height: 2.4vw;
          margin-top: 0.54vw;
        }
        .text-group_13 {
          width: 16.27vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
        }
      }
      .group_24 {
        width: 38.94vw;
        height: 7.2vw;
        margin: 2.93vw 0 0 3.46vw;
        .text-wrapper_21 {
          border-radius: 4px;
          height: 7.2vw;
          border: 1px solid rgba(11, 206, 148, 1);
          width: 17.87vw;
          .text_47 {
            width: 13.6vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.86vw 0 0 2.13vw;
          }
        }
        .text-wrapper_22 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 7.2vw;
          width: 17.87vw;
          .text_48 {
            width: 13.07vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.86vw 0 0 2.93vw;
          }
        }
      }
      .group_25 {
        width: 38.94vw;
        height: 3.74vw;
        margin: 4.53vw 0 1.86vw 3.46vw;
        .box_21 {
          background-color: rgba(150, 150, 150, 1);
          width: 4.27vw;
          height: 3.74vw;
        }
        .text_49 {
          width: 2.14vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 0.26vw 0 0 0.8vw;
        }
        .box_22 {
          background-color: rgba(150, 150, 150, 1);
          width: 4vw;
          height: 3.74vw;
          margin-left: 6.67vw;
        }
        .text_50 {
          width: 2.14vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 0.26vw 0 0 0.8vw;
        }
        .image-text_14 {
          width: 11.2vw;
          height: 3.2vw;
          margin: 0.26vw 0 0 6.93vw;
          .group_26 {
            background-color: rgba(150, 150, 150, 1);
            width: 3.47vw;
            height: 3.2vw;
          }
          .text-group_14 {
            width: 6.67vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
          }
        }
      }
    }
    .text-wrapper_23 {
      position: absolute;
      left: -105.33vw;
      top: 235.74vw;
      width: 44.8vw;
      height: 44.8vw;
      background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNGa911c964ed32937659ea639dfb0c05a2.png)
        100% no-repeat;
      background-size: 100% 100%;
      .text_51 {
        width: 6.4vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 31.46vw 0 0 114.66vw;
      }
      .text_52 {
        width: 6.4vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 31.46vw -101.06vw 0 18.4vw;
      }
    }
    .text-wrapper_24 {
      background-color: rgba(16, 16, 16, 0.1);
      border-radius: 100px;
      height: 6.14vw;
      width: 18.67vw;
      position: absolute;
      left: 87.47vw;
      top: 26.94vw;
      .text_53 {
        width: 13.87vw;
        height: 5.07vw;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 3.46vw;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 3.47vw;
        margin: 0.53vw 0 0 2.4vw;
      }
    }
    .group_27 {
      background-color: rgba(255, 255, 255, 1);
      position: absolute;
      left: 0;
      top: 180.54vw;
      width: 100vw;
      height: 13.07vw;
      .image-text_15 {
        width: 6.4vw;
        height: 10.4vw;
        margin-top: 1.6vw;
        .label_2 {
          width: 5.87vw;
          height: 5.87vw;
          margin-left: 0.27vw;
        }
        .text-group_15 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 1.34vw;
        }
      }
      .image-text_16 {
        width: 6.4vw;
        height: 10.14vw;
        margin-top: 1.87vw;
        .block_6 {
          height: 5.87vw;
          background: url(/static/lanhu_shouyejishikapian/a1163f772a4e46fcade03d5393284bde_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 0.27vw;
          width: 5.87vw;
          .group_28 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 50%;
            width: 1.87vw;
            height: 1.87vw;
            margin: 0.8vw 0 0 2.66vw;
          }
        }
        .text-group_16 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 1.07vw;
        }
      }
      .image-text_17 {
        width: 6.4vw;
        height: 10.14vw;
        margin-top: 1.87vw;
        .label_3 {
          width: 5.87vw;
          height: 5.87vw;
          margin-left: 0.27vw;
        }
        .text-group_17 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 1.07vw;
        }
      }
      .image-text_18 {
        width: 6.4vw;
        height: 10.14vw;
        margin-top: 1.87vw;
        .label_4 {
          width: 5.87vw;
          height: 5.87vw;
          margin-left: 0.27vw;
        }
        .text-group_18 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 1.07vw;
        }
      }
    }
    .text-wrapper_25 {
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 5px;
      height: 9.07vw;
      width: 25.07vw;
      position: absolute;
      left: 18.4vw;
      top: 129.6vw;
      .text_54 {
        width: 14.94vw;
        height: 3.74vw;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 3.73vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 2.66vw 0 0 5.06vw;
      }
    }
  }
}
