<template>
  <view class="page flex-col">
    <view class="box_1 flex-col">
      <view class="group_1 flex-row">
        <text class="text_1">12:30</text>
        <image
          class="thumbnail_1"
          referrerpolicy="no-referrer"
          src="/static/lanhu_tianjiadizhi_5/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png"
        />
        <image
          class="thumbnail_2"
          referrerpolicy="no-referrer"
          src="/static/lanhu_tianjiadizhi_5/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png"
        />
        <image
          class="thumbnail_3"
          referrerpolicy="no-referrer"
          src="/static/lanhu_tianjiadizhi_5/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png"
        />
      </view>
      <view class="group_2 flex-row">
        <image
          class="thumbnail_4"
          referrerpolicy="no-referrer"
          src="/static/lanhu_tianjiadizhi_5/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png"
        />
        <text class="text_2">订单</text>
        <image
          class="image_1"
          referrerpolicy="no-referrer"
          src="/static/lanhu_tianjiadizhi_5/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png"
        />
      </view>
      <!-- tabs订单状态切换 -->
      <view class="group_3 flex-col">
        <view class="text-wrapper_1 flex-row">
          <text class="text_3" :class="{ active: currentStatus === '全部' }" @click="switchStatus('全部')">全部</text>
          <text class="text_4" :class="{ active: currentStatus === '待支付' }" @click="switchStatus('待支付')">待支付</text>
          <text class="text_5" :class="{ active: currentStatus === '待服务' }" @click="switchStatus('待服务')">待服务</text>
          <text class="text_6" :class="{ active: currentStatus === '服务中' }" @click="switchStatus('服务中')">服务中</text>
          <text class="text_7" :class="{ active: currentStatus === '已完成' }" @click="switchStatus('已完成')">已完成</text>
        </view>
        <view class="box_2 flex-row">
          <view class="text-wrapper_2 flex-col">
            <text class="text_8">1</text>
          </view>
        </view>
      </view>
      <!-- tabs订单状态切换结束 -->
    </view>

    <!-- 全部订单列表 -->
    <view class="box_3 flex-col">

      <!-- 订单卡片循环 -->
      <view v-for="(order, index) in filteredOrderList" :key="order.id || index" class="order-card">
        <!-- 订单头部信息 -->
        <view class="text-wrapper_3 flex-row justify-between">
          <text class="text_9">订单号：{{ order.orderNo }}</text>
          <text class="text_10">{{ order.status }}</text>
        </view>

        <!-- 订单详情 -->
        <view class="group_4 flex-row justify-between">
          <view class="image-text_1 flex-row justify-between">
            <image
              class="image_2"
              referrerpolicy="no-referrer"
              :src="order.serviceImage || '/static/lanhu_tianjiadizhi_5/FigmaDDSSlicePNGb7d6e462c2527f7ccdf81162f3f33bd8.png'"
            />
            <view class="text-group_1 flex-col">
              <text class="text_11">{{ order.placeholder || '占位' }}</text>
              <text class="text_12">{{ order.serviceName }}</text>
              <text class="text_13">服务技师&nbsp;&nbsp;{{ order.technicianName }}</text>
              <text class="text_14">预约时间：{{ order.appointmentTime }}</text>
              <view class="text-wrapper_4">
                <text class="text_15">总计:</text>
                <text class="text_16">￥</text>
                <text class="text_17">{{ order.totalAmount }}</text>
              </view>
            </view>
          </view>
          <text class="text_18">x{{ order.quantity || 1 }}</text>
        </view>

        <!-- 订单操作按钮 -->
        <view class="group_5 flex-row" :class="getButtonLayoutClass(order.status)">
          <!-- 待支付状态按钮 -->
          <template v-if="order.status === '待支付'">
            <view class="text-wrapper_7 flex-col" @click="goToPay(order)">
              <text class="text_21">去支付</text>
            </view>
          </template>

          <!-- 待服务状态按钮 -->
          <template v-else-if="order.status === '待服务'">
            <view class="text-wrapper_6 flex-col" @click="contactService(order)">
              <text class="text_20">申请退款</text>
            </view>
          </template>

          <!-- 服务中状态按钮 -->
          <template v-else-if="order.status === '服务中'">
            <view class="text-wrapper_7 flex-col" @click="viewProgress(order)">
              <text class="text_21">升级/加钟</text>
            </view>
            <view class="text-wrapper_7 flex-col" @click="viewProgress(order)">
              <text class="text_21">完成服务</text>
            </view>
          </template>

          <!-- 已完成状态按钮 -->
          <template v-else-if="order.status === '已完成'">
            <view class="text-wrapper_5 flex-col" @click="deleteOrder(order, index)">
              <text class="text_19">删除</text>
            </view>
            <view class="text-wrapper_6 flex-col" @click="goToEvaluate(order)">
              <text class="text_20">去评价</text>
            </view>
            <view class="text-wrapper_7 flex-col" @click="reorder(order)">
              <text class="text_21">再来一单</text>
            </view>
            <view class="text-wrapper_8 flex-col" @click="giveTip(order)">
              <text class="text_22">打赏</text>
            </view>
          </template>
        </view>
      </view>
      <!-- 订单卡片循环结束 -->

    </view>

  </view>

</template>
<script>
export default {
  data() {
    return {
      constants: {},
      // 当前选中的订单状态
      currentStatus: '全部',
      // 订单列表数据
      orderList: [
        {
          id: 1,
          orderNo: '2022441545646545645...',
          status: '已完成',
          serviceImage: '/static/lanhu_tianjiadizhi_5/FigmaDDSSlicePNGb7d6e462c2527f7ccdf81162f3f33bd8.png',
          placeholder: '占位',
          serviceName: '服务的项目名称',
          technicianName: '冉',
          appointmentTime: '2024-10  00:23',
          totalAmount: '298.00',
          quantity: 1
        },
        {
          id: 2,
          orderNo: '2022441545646545646...',
          status: '待服务',
          serviceImage: '/static/lanhu_tianjiadizhi_5/FigmaDDSSlicePNGb7d6e462c2527f7ccdf81162f3f33bd8.png',
          placeholder: '占位',
          serviceName: '全身按摩服务',
          technicianName: '李师傅',
          appointmentTime: '2024-10  14:30',
          totalAmount: '398.00',
          quantity: 1
        },
        {
          id: 3,
          orderNo: '2022441545646545647...',
          status: '服务中',
          serviceImage: '/static/lanhu_tianjiadizhi_5/FigmaDDSSlicePNGb7d6e462c2527f7ccdf81162f3f33bd8.png',
          placeholder: '占位',
          serviceName: '足底按摩',
          technicianName: '王师傅',
          appointmentTime: '2024-10  16:00',
          totalAmount: '198.00',
          quantity: 2
        },
        {
          id: 4,
          orderNo: '2022441545646545648...',
          status: '待支付',
          serviceImage: '/static/lanhu_tianjiadizhi_5/FigmaDDSSlicePNGb7d6e462c2527f7ccdf81162f3f33bd8.png',
          placeholder: '占位',
          serviceName: '肩颈按摩',
          technicianName: '张师傅',
          appointmentTime: '2024-10  18:00',
          totalAmount: '158.00',
          quantity: 1
        },
        {
          id: 5,
          orderNo: '2022441545646545649...',
          status: '待支付',
          serviceImage: '/static/lanhu_tianjiadizhi_5/FigmaDDSSlicePNGb7d6e462c2527f7ccdf81162f3f33bd8.png',
          placeholder: '占位',
          serviceName: '全身SPA',
          technicianName: '刘师傅',
          appointmentTime: '2024-10  20:00',
          totalAmount: '588.00',
          quantity: 1
        }
      ]
    };
  },
  computed: {
    // 根据当前状态筛选订单列表
    filteredOrderList() {
      if (this.currentStatus === '全部') {
        return this.orderList;
      }
      return this.orderList.filter(order => order.status === this.currentStatus);
    }
  },
  methods: {
    // 切换订单状态
    switchStatus(status) {
      this.currentStatus = status;
    },

    // 根据订单状态获取按钮布局类
    getButtonLayoutClass(status) {
      // 所有按钮都从右侧开始往左排列
      return 'justify-end';
    },

    // 取消订单
    cancelOrder(order, index) {
      uni.showModal({
        title: '确认取消',
        content: `确定要取消订单号为 ${order.orderNo} 的订单吗？`,
        success: (res) => {
          if (res.confirm) {
            // 从原数组中找到对应订单并删除
            const originalIndex = this.orderList.findIndex(item => item.id === order.id);
            if (originalIndex !== -1) {
              this.orderList.splice(originalIndex, 1);
            }
            uni.showToast({
              title: '订单已取消',
              icon: 'success'
            });
          }
        }
      });
    },

    // 去支付
    goToPay(order) {
      uni.showToast({
        title: '跳转到支付页面...',
        icon: 'loading'
      });
      // TODO: 跳转到支付页面
      setTimeout(() => {
        uni.navigateTo({
          url: `/pages/payment/index?orderId=${order.id}&amount=${order.totalAmount}`
        });
      }, 1000);
    },

    // 联系客服
    contactService(order) {
      uni.showToast({
        title: '正在为您接通客服...',
        icon: 'loading'
      });
      // TODO: 联系客服功能
    },

    // 查看进度
    viewProgress(order) {
      uni.navigateTo({
        url: `/pages/progress/index?orderId=${order.id}`
      });
    },

    // 删除订单
    deleteOrder(order, index) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除订单号为 ${order.orderNo} 的订单吗？`,
        success: (res) => {
          if (res.confirm) {
            this.orderList.splice(index, 1);
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    },

    // 去评价
    goToEvaluate(order) {
      // TODO: 跳转到评价页面
      uni.navigateTo({
        url: `/pages/evaluate/index?orderId=${order.id}`
      });
    },

    // 再来一单
    reorder(order) {
      // TODO: 重新下单逻辑
      uni.showToast({
        title: '正在为您重新下单...',
        icon: 'loading'
      });
      // 这里可以调用重新下单的API
    },

    // 打赏
    giveTip(order) {
      // TODO: 跳转到打赏页面
      uni.navigateTo({
        url: `/pages/tip/index?orderId=${order.id}`
      });
    }
  }
};
</script>
<style lang='scss'>
@import '../common/common.scss';
@import './assets/style/index.rpx.scss';
</style>
