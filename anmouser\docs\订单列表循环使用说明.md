# 订单列表循环使用说明

## 概述
已成功将 `anmouser/src/pages/lanhu_tianjiadizhi_5/index.vue` 中的订单卡片改为循环生成，支持动态绑定订单数据。

## 数据结构

### orderList 数组结构
```javascript
orderList: [
  {
    id: 1,                    // 订单ID（必需，用作key）
    orderNo: '202244154...',  // 订单号
    status: '已完成',         // 订单状态
    serviceImage: '/static/...',  // 服务图片路径
    placeholder: '占位',      // 占位文本
    serviceName: '服务项目名称',  // 服务名称
    technicianName: '技师姓名',   // 技师姓名
    appointmentTime: '2024-10 00:23',  // 预约时间
    totalAmount: '298.00',    // 总金额
    quantity: 1               // 数量
  }
]
```

## 主要功能

### 1. 订单卡片循环渲染
- 使用 `v-for="(order, index) in orderList"` 循环生成订单卡片
- 每个订单卡片包含：订单头部、订单详情、操作按钮

### 2. 数据绑定
- **订单号**: `{{ order.orderNo }}`
- **订单状态**: `{{ order.status }}`
- **服务图片**: `:src="order.serviceImage"`
- **服务名称**: `{{ order.serviceName }}`
- **技师姓名**: `{{ order.technicianName }}`
- **预约时间**: `{{ order.appointmentTime }}`
- **总金额**: `{{ order.totalAmount }}`
- **数量**: `{{ order.quantity || 1 }}`

### 3. 交互功能
- **删除订单**: `@click="deleteOrder(order, index)"`
- **去评价**: `@click="goToEvaluate(order)"`
- **再来一单**: `@click="reorder(order)"`
- **打赏**: `@click="giveTip(order)"`

## 样式优化
- 添加了 `.order-card` 样式类
- 订单卡片间距：`margin-bottom: 20rpx`
- 分割线：`border-bottom: 1rpx solid #f0f0f0`
- 最后一个卡片无下边距和分割线

## 使用方法

### 1. 动态加载订单数据
```javascript
// 在 methods 中添加获取订单数据的方法
async loadOrderList() {
  try {
    const res = await uni.request({
      url: '/api/orders',
      method: 'GET'
    });
    this.orderList = res.data.list;
  } catch (error) {
    console.error('获取订单列表失败:', error);
  }
}
```

### 2. 页面加载时获取数据
```javascript
onLoad() {
  this.loadOrderList();
}
```

### 3. 自定义订单状态样式
可以根据订单状态动态设置样式：
```html
<text class="text_10" :class="getStatusClass(order.status)">{{ order.status }}</text>
```

```javascript
getStatusClass(status) {
  const statusMap = {
    '已完成': 'status-completed',
    '待支付': 'status-pending',
    '待服务': 'status-waiting',
    '服务中': 'status-processing'
  };
  return statusMap[status] || '';
}
```

## 注意事项
1. 确保每个订单对象都有唯一的 `id` 字段作为 `key`
2. 图片路径需要确保存在，否则会显示默认图片
3. 金额字段建议使用字符串格式，避免精度问题
4. 时间格式建议统一处理，可以使用过滤器或计算属性

## 扩展建议
1. 添加订单筛选功能（按状态筛选）
2. 添加下拉刷新和上拉加载更多
3. 添加订单搜索功能
4. 优化图片懒加载
5. 添加骨架屏加载效果
