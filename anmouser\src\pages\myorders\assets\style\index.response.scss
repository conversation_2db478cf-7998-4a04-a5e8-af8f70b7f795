.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 100vw;
  height: 216.54vw;
  overflow: hidden;
  .box_1 {
    width: 100vw;
    height: 37.6vw;
    .group_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 8.54vw;
      .text_1 {
        width: 8.54vw;
        height: 4.8vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 3.2vw;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 40vw;
        margin: 1.86vw 0 0 4.26vw;
      }
      .thumbnail_1 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 66.93vw;
      }
      .thumbnail_2 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 0.8vw;
      }
      .thumbnail_3 {
        width: 5.07vw;
        height: 5.07vw;
        margin: 1.86vw 4vw 0 0.8vw;
      }
    }
    .group_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 14.4vw;
      .thumbnail_4 {
        width: 2.4vw;
        height: 4.54vw;
        margin: 5.06vw 0 0 4.8vw;
      }
      .text_2 {
        width: 8.54vw;
        height: 5.87vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 4.26vw;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 5.87vw;
        margin: 4.26vw 0 0 2.93vw;
      }
      .image_1 {
        width: 23.2vw;
        height: 8.54vw;
        margin: 2.93vw 1.6vw 0 56.53vw;
      }
    }
    .group_3 {
      background-color: rgba(255, 255, 255, 1);
      height: 11.74vw;
      margin-bottom: 2.94vw;
      width: 100vw;
      position: relative;
      .text-wrapper_1 {
        width: 89.34vw;
        height: 3.74vw;
        margin: 4vw 0 0 6.4vw;
        .text_3 {
          width: 7.47vw;
          height: 3.74vw;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 3.73vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
        .text_4 {
          width: 11.2vw;
          height: 3.74vw;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 3.73vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-left: 10.67vw;
        }
        .text_5 {
          width: 11.2vw;
          height: 3.74vw;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 3.73vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-left: 8.8vw;
        }
        .text_6 {
          width: 11.2vw;
          height: 3.74vw;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 3.73vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-left: 8.8vw;
        }
        .text_7 {
          width: 11.2vw;
          height: 3.74vw;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 3.73vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-left: 8.8vw;
        }
      }
      .box_2 {
        position: absolute;
        left: 12vw;
        top: 1.6vw;
        width: 3.74vw;
        height: 3.74vw;
        .text-wrapper_2 {
          background-color: rgba(238, 12, 12, 1);
          border-radius: 50%;
          height: 3.74vw;
          width: 3.74vw;
          .text_8 {
            width: 1.6vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 2.66vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 0.53vw 0 0 1.06vw;
          }
        }
      }
    }
  }
  .box_3 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 54.4vw;
    width: 93.6vw;
    margin: -0.26vw 0 0 3.2vw;
    .text-wrapper_3 {
      width: 87.47vw;
      height: 3.47vw;
      margin: 4.8vw 0 0 3.2vw;
      .text_9 {
        width: 55.47vw;
        height: 3.47vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 3.46vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
      .text_10 {
        width: 9.6vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 0.27vw;
      }
    }
    .group_4 {
      width: 87.2vw;
      height: 21.07vw;
      margin: 9.6vw 0 0 2.93vw;
      .image-text_1 {
        width: 62.94vw;
        height: 21.07vw;
        .image_2 {
          width: 20.8vw;
          height: 20.8vw;
        }
        .text-group_1 {
          width: 40vw;
          height: 21.07vw;
          .text_11 {
            width: 24vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 4vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
          }
          .text_12 {
            width: 24vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 4vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: -3.73vw 0 0 0.53vw;
          }
          .text_13 {
            width: 18.14vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 2.66vw 0 0 0.53vw;
          }
          .text_14 {
            width: 39.47vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.86vw 0 0 0.53vw;
          }
          .text-wrapper_4 {
            width: 23.74vw;
            height: 4vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.86vw 0 0 0.53vw;
            .text_15 {
              width: 23.74vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text_16 {
              width: 23.74vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text_17 {
              width: 23.74vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 4vw;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
          }
        }
      }
      .text_18 {
        width: 3.47vw;
        height: 3.74vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 3.73vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 0.27vw;
      }
    }
    .group_5 {
      width: 80.54vw;
      height: 6.67vw;
      margin: 4.53vw 0 4.26vw 10.4vw;
      .text-wrapper_5 {
        border-radius: 4px;
        height: 6.67vw;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 18.14vw;
        .text_19 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 5.86vw;
        }
      }
      .text-wrapper_6 {
        border-radius: 4px;
        height: 6.67vw;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 2.67vw;
        width: 18.14vw;
        .text_20 {
          width: 9.6vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 4.26vw;
        }
      }
      .text-wrapper_7 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 6.67vw;
        margin-left: 2.67vw;
        width: 18.14vw;
        .text_21 {
          width: 12.8vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 2.66vw;
        }
      }
      .text-wrapper_8 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 6.67vw;
        margin-left: 2.67vw;
        width: 18.14vw;
        .text_22 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 5.86vw;
        }
      }
    }
  }
  .box_4 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 54.4vw;
    width: 93.6vw;
    margin: 2.66vw 0 0 3.2vw;
    .text-wrapper_9 {
      width: 87.47vw;
      height: 3.47vw;
      margin: 4.8vw 0 0 3.2vw;
      .text_23 {
        width: 55.47vw;
        height: 3.47vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 3.46vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
      .text_24 {
        width: 9.6vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 0.27vw;
      }
    }
    .block_1 {
      width: 87.2vw;
      height: 21.07vw;
      margin: 9.6vw 0 0 2.93vw;
      .image-text_2 {
        width: 62.94vw;
        height: 21.07vw;
        .image_3 {
          width: 20.8vw;
          height: 20.8vw;
        }
        .text-group_2 {
          width: 40vw;
          height: 21.07vw;
          .text_25 {
            width: 24vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 4vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
          }
          .text_26 {
            width: 24vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 4vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: -3.73vw 0 0 0.53vw;
          }
          .text_27 {
            width: 18.14vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 2.66vw 0 0 0.53vw;
          }
          .text_28 {
            width: 39.47vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.86vw 0 0 0.53vw;
          }
          .text-wrapper_10 {
            width: 23.74vw;
            height: 4vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.86vw 0 0 0.53vw;
            .text_29 {
              width: 23.74vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text_30 {
              width: 23.74vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text_31 {
              width: 23.74vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 4vw;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
          }
        }
      }
      .text_32 {
        width: 3.47vw;
        height: 3.74vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 3.73vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 0.27vw;
      }
    }
    .block_2 {
      width: 80.54vw;
      height: 6.67vw;
      margin: 4.53vw 0 4.26vw 10.4vw;
      .text-wrapper_11 {
        border-radius: 4px;
        height: 6.67vw;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 18.14vw;
        .text_33 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 5.86vw;
        }
      }
      .text-wrapper_12 {
        border-radius: 4px;
        height: 6.67vw;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 2.67vw;
        width: 18.14vw;
        .text_34 {
          width: 9.6vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 4.26vw;
        }
      }
      .text-wrapper_13 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 6.67vw;
        margin-left: 2.67vw;
        width: 18.14vw;
        .text_35 {
          width: 12.8vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 2.66vw;
        }
      }
      .text-wrapper_14 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 6.67vw;
        margin-left: 2.67vw;
        width: 18.14vw;
        .text_36 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 5.86vw;
        }
      }
    }
  }
  .box_5 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 54.4vw;
    width: 93.6vw;
    margin: 2.66vw 0 10.66vw 3.2vw;
    .text-wrapper_15 {
      width: 87.47vw;
      height: 3.47vw;
      margin: 4.8vw 0 0 3.2vw;
      .text_37 {
        width: 55.47vw;
        height: 3.47vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 3.46vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
      .text_38 {
        width: 9.6vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 0.27vw;
      }
    }
    .group_6 {
      width: 87.2vw;
      height: 21.07vw;
      margin: 9.6vw 0 0 2.93vw;
      .image-text_3 {
        width: 62.94vw;
        height: 21.07vw;
        .image_4 {
          width: 20.8vw;
          height: 20.8vw;
        }
        .text-group_3 {
          width: 40vw;
          height: 21.07vw;
          .text_39 {
            width: 24vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 4vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
          }
          .text_40 {
            width: 24vw;
            height: 4vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 4vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: -3.73vw 0 0 0.53vw;
          }
          .text_41 {
            width: 18.14vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 2.66vw 0 0 0.53vw;
          }
          .text_42 {
            width: 39.47vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.86vw 0 0 0.53vw;
          }
          .text-wrapper_16 {
            width: 23.74vw;
            height: 4vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.86vw 0 0 0.53vw;
            .text_43 {
              width: 23.74vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text_44 {
              width: 23.74vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text_45 {
              width: 23.74vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 4vw;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
            }
          }
        }
      }
      .text_46 {
        width: 3.47vw;
        height: 3.74vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 3.73vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 0.27vw;
      }
    }
    .group_7 {
      width: 80.54vw;
      height: 6.67vw;
      margin: 4.53vw 0 4.26vw 10.4vw;
      .text-wrapper_17 {
        border-radius: 4px;
        height: 6.67vw;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 18.14vw;
        .text_47 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 5.86vw;
        }
      }
      .text-wrapper_18 {
        border-radius: 4px;
        height: 6.67vw;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 2.67vw;
        width: 18.14vw;
        .text_48 {
          width: 9.6vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 4.26vw;
        }
      }
      .text-wrapper_19 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 6.67vw;
        margin-left: 2.67vw;
        width: 18.14vw;
        .text_49 {
          width: 12.8vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 2.66vw;
        }
      }
      .text-wrapper_20 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 6.67vw;
        margin-left: 2.67vw;
        width: 18.14vw;
        .text_50 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 1.86vw 0 0 5.86vw;
        }
      }
    }
  }
}
