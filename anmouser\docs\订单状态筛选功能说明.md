# 订单状态筛选功能说明

## 功能概述
实现了订单状态筛选功能，用户可以点击不同的状态标签来查看对应状态的订单，并且每种状态的订单显示不同的操作按钮。

## 主要功能

### 1. 状态筛选
- **全部**: 显示所有订单
- **待支付**: 只显示未支付的订单
- **待服务**: 只显示等待服务的订单  
- **服务中**: 只显示正在服务的订单
- **已完成**: 只显示已完成的订单

### 2. 状态切换
- 点击状态标签可切换显示对应状态的订单
- 当前选中状态会高亮显示（绿色字体）
- 使用计算属性 `filteredOrderList` 实时筛选订单

### 3. 不同状态的操作按钮

#### 待支付状态
- **取消订单**: 取消未支付的订单
- **去支付**: 跳转到支付页面

#### 待服务状态  
- **取消订单**: 取消等待服务的订单
- **联系客服**: 联系客服咨询

#### 服务中状态
- **联系客服**: 联系客服咨询
- **查看进度**: 查看服务进度

#### 已完成状态
- **删除**: 删除已完成的订单
- **去评价**: 跳转到评价页面
- **再来一单**: 重新下单
- **打赏**: 给技师打赏

## 技术实现

### 1. 数据结构
```javascript
data() {
  return {
    currentStatus: '全部',  // 当前选中状态
    orderList: [...]       // 订单列表
  }
}
```

### 2. 计算属性
```javascript
computed: {
  filteredOrderList() {
    if (this.currentStatus === '全部') {
      return this.orderList;
    }
    return this.orderList.filter(order => order.status === this.currentStatus);
  }
}
```

### 3. 状态切换方法
```javascript
switchStatus(status) {
  this.currentStatus = status;
}
```

### 4. 模板条件渲染
```html
<!-- 待支付状态按钮 -->
<template v-if="order.status === '待支付'">
  <view @click="cancelOrder(order, index)">取消订单</view>
  <view @click="goToPay(order)">去支付</view>
</template>
```

## 样式特性

### 1. 状态激活样式
```scss
.text_3, .text_4, .text_5, .text_6, .text_7 {
  &.active {
    color: rgba(11, 206, 148, 1) !important;
    font-weight: 500;
  }
}
```

### 2. 悬停效果
```scss
&:hover {
  color: rgba(11, 206, 148, 0.8);
}
```

## 示例数据
已添加5个示例订单，包含不同状态：
- 1个已完成订单
- 1个待服务订单  
- 1个服务中订单
- 2个待支付订单

## 使用方法

### 1. 切换订单状态
点击顶部的状态标签（全部、待支付、待服务、服务中、已完成）

### 2. 操作订单
根据订单状态，点击相应的操作按钮：
- 待支付订单：可以取消或去支付
- 其他状态：根据业务逻辑显示对应按钮

### 3. 自定义状态
如需添加新状态，需要：
1. 在模板中添加新的状态标签
2. 在 `switchStatus` 方法中处理新状态
3. 在操作按钮部分添加新状态的条件判断
4. 添加对应的处理方法

## 注意事项

1. **数据一致性**: 取消订单时使用 `findIndex` 确保从原数组中正确删除
2. **状态同步**: 筛选使用计算属性，确保状态切换时列表实时更新
3. **按钮逻辑**: 不同状态显示不同按钮，避免用户误操作
4. **用户体验**: 添加了加载提示和确认弹窗

## 扩展建议

1. **状态统计**: 在每个状态标签旁显示对应状态的订单数量
2. **状态更新**: 支付成功后自动更新订单状态
3. **实时刷新**: 定期从服务器获取最新订单状态
4. **状态流转**: 添加订单状态流转的可视化展示
5. **批量操作**: 支持批量取消或批量操作订单
