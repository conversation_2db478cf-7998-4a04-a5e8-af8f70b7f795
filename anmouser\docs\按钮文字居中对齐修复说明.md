# 按钮文字居中对齐修复说明

## 问题描述
所有订单状态按钮中的文字需要在水平和垂直方向都完美居中对齐。

## 问题原因
原始样式使用固定的 `margin` 值来定位按钮文字，这种方式无法确保文字在不同长度的情况下都能完美居中。

## 解决方案

### 1. 使用 Flexbox 布局
为所有按钮容器添加 Flexbox 属性：
```scss
display: flex;
align-items: center;      // 垂直居中
justify-content: center;  // 水平居中
```

### 2. 移除固定定位
移除文字元素的固定 `width`、`height` 和 `margin` 属性，让 Flexbox 自动处理居中对齐。

## 修改的按钮样式类

### 循环订单卡片按钮
- `.text-wrapper_5` - 取消订单/删除按钮
- `.text-wrapper_6` - 申请退款/去评价按钮  
- `.text-wrapper_7` - 去支付/再来一单/升级加钟/完成服务按钮
- `.text-wrapper_8` - 打赏按钮

### 静态订单卡片按钮（保持一致性）
- `.text-wrapper_11` - 删除按钮
- `.text-wrapper_12` - 去评价按钮
- `.text-wrapper_13` - 再来一单按钮
- `.text-wrapper_14` - 打赏按钮
- `.text-wrapper_17` - 删除按钮
- `.text-wrapper_18` - 去评价按钮
- `.text-wrapper_19` - 再来一单按钮
- `.text-wrapper_20` - 打赏按钮

## 修改前后对比

### 修改前：
```scss
.text-wrapper_5 {
  border-radius: 4px;
  height: 50rpx;
  border: 1px solid rgba(153, 153, 153, 1);
  width: 136rpx;
  .text_19 {
    width: 48rpx;
    height: 24rpx;
    // ... 其他样式
    margin: 14rpx 0 0 44rpx;  // 固定定位
  }
}
```

### 修改后：
```scss
.text-wrapper_5 {
  border-radius: 4px;
  height: 50rpx;
  border: 1px solid rgba(153, 153, 153, 1);
  width: 136rpx;
  display: flex;              // 新增
  align-items: center;        // 新增：垂直居中
  justify-content: center;    // 新增：水平居中
  .text_19 {
    // 移除了 width, height, margin
    // ... 其他样式保持不变
  }
}
```

## 技术优势

### 1. 响应式居中
- 无论按钮文字长度如何变化，都能自动居中
- 支持不同字体大小的完美居中

### 2. 维护性更好
- 不需要手动计算 margin 值
- 添加新按钮时只需要应用相同的 Flexbox 样式

### 3. 兼容性良好
- Flexbox 在现代浏览器和小程序中都有良好支持
- uni-app 框架完全支持 Flexbox 布局

## 样式特性保持

### 保持的属性：
- `border-radius: 4px` - 圆角
- `height: 50rpx` - 按钮高度
- `width: 136rpx` - 按钮宽度
- `background-color` - 背景色（绿色按钮）
- `border` - 边框（灰色按钮）
- `font-size: 24rpx` - 字体大小
- `color` - 文字颜色
- `white-space: nowrap` - 防止文字换行

### 移除的属性：
- 文字的固定 `width` 和 `height`
- 文字的 `margin` 定位

## 应用场景

### 1. 不同状态按钮
- **待支付**: 取消订单、去支付
- **待服务**: 取消订单、申请退款
- **服务中**: 升级/加钟、完成服务
- **已完成**: 删除、去评价、再来一单、打赏

### 2. 不同文字长度
- 短文字：删除、打赏
- 中等文字：去支付、去评价
- 长文字：取消订单、申请退款、升级/加钟、完成服务

## 测试建议

### 1. 视觉测试
- 检查所有按钮文字是否水平居中
- 检查所有按钮文字是否垂直居中
- 验证不同状态下的按钮显示效果

### 2. 功能测试
- 确认按钮点击区域正常
- 验证按钮样式在不同设备上的表现
- 测试按钮在不同屏幕尺寸下的显示效果

## 注意事项

1. **保持一致性**: 所有按钮都使用相同的居中方式
2. **文字长度**: 确保按钮宽度足够容纳最长的文字
3. **行高设置**: 保持 `line-height` 与 `font-size` 的合理比例
4. **兼容性**: 在不同平台测试 Flexbox 的表现

## 扩展建议

1. **统一按钮组件**: 可以考虑创建统一的按钮组件
2. **动态宽度**: 根据文字长度动态调整按钮宽度
3. **主题支持**: 支持不同的按钮主题和样式
4. **无障碍优化**: 添加适当的无障碍属性
