.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
  .box_1 {
    width: 750rpx;
    height: 282rpx;
    .group_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 750rpx;
      height: 64rpx;
      .text_1 {
        width: 64rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 24rpx;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin: 14rpx 0 0 32rpx;
      }
      .thumbnail_1 {
        width: 36rpx;
        height: 36rpx;
        margin: 14rpx 0 0 502rpx;
      }
      .thumbnail_2 {
        width: 36rpx;
        height: 36rpx;
        margin: 14rpx 0 0 6rpx;
      }
      .thumbnail_3 {
        width: 38rpx;
        height: 38rpx;
        margin: 14rpx 30rpx 0 6rpx;
      }
    }
    .group_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 750rpx;
      height: 108rpx;
      .thumbnail_4 {
        width: 18rpx;
        height: 34rpx;
        margin: 38rpx 0 0 36rpx;
      }
      .text_2 {
        width: 64rpx;
        height: 44rpx;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 32rpx;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 44rpx;
        margin: 32rpx 0 0 22rpx;
      }
      .image_1 {
        width: 174rpx;
        height: 64rpx;
        margin: 22rpx 12rpx 0 424rpx;
      }
    }
    .group_3 {
      background-color: rgba(255, 255, 255, 1);
      height: 88rpx;
      margin-bottom: 22rpx;
      width: 750rpx;
      position: relative;
      .text-wrapper_1 {
        width: 670rpx;
        height: 28rpx;
        margin: 30rpx 0 0 48rpx;
        .text_3 {
          width: 56rpx;
          height: 28rpx;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
        }
        .text_4 {
          width: 84rpx;
          height: 28rpx;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
          margin-left: 80rpx;
        }
        .text_5 {
          width: 84rpx;
          height: 28rpx;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
          margin-left: 66rpx;
        }
        .text_6 {
          width: 84rpx;
          height: 28rpx;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
          margin-left: 66rpx;
        }
        .text_7 {
          width: 84rpx;
          height: 28rpx;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
          margin-left: 66rpx;
        }
      }
      .box_2 {
        position: absolute;
        left: 90rpx;
        top: 12rpx;
        width: 28rpx;
        height: 28rpx;
        .text-wrapper_2 {
          background-color: rgba(238, 12, 12, 1);
          border-radius: 50%;
          height: 28rpx;
          width: 28rpx;
          .text_8 {
            width: 12rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 20rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 20rpx;
            margin: 4rpx 0 0 8rpx;
          }
        }
      }
    }
  }
  .box_3 {
    // 移除背景色和固定高度，现在只作为容器
    width: 750rpx;
    margin: -2rpx 0 0 0;
    .text-wrapper_3 {
      width: 656rpx;
      height: 26rpx;
      margin: 36rpx 0 0 24rpx;
      .text_9 {
        width: 416rpx;
        height: 26rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
      }
      .text_10 {
        width: 72rpx;
        height: 24rpx;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin-top: 2rpx;
      }
    }
    .group_4 {
      width: 654rpx;
      height: 158rpx;
      margin: 72rpx 0 0 22rpx;
      .image-text_1 {
        width: 472rpx;
        height: 158rpx;
        .image_2 {
          width: 156rpx;
          height: 156rpx;
        }
        .text-group_1 {
          width: 300rpx;
          height: 158rpx;
          .text_11 {
            width: 180rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 30rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 30rpx;
          }
          .text_12 {
            width: 180rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 30rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 30rpx;
            margin: -28rpx 0 0 4rpx;
          }
          .text_13 {
            width: 136rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 20rpx 0 0 0;
          }
          .text_14 {
            width: 296rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 14rpx 0 0 0;
          }
          .text-wrapper_4 {
            width: 178rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 30rpx;
            margin: 14rpx 0 0 0;
            .text_15 {
              width: 178rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_16 {
              width: 178rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_17 {
              width: 178rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 30rpx;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 30rpx;
            }
          }
        }
      }
      .text_18 {
        width: 26rpx;
        height: 28rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 28rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 28rpx;
        margin-top: 2rpx;
      }
    }
    .group_5 {
      width: 604rpx;
      height: 50rpx;
      margin: 34rpx 0 32rpx 78rpx;
      .text-wrapper_5 {
        border-radius: 4px;
        height: 50rpx;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_19 {
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
      .text-wrapper_6 {
        border-radius: 4px;
        height: 50rpx;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 20rpx;
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_20 {
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
      .text-wrapper_7 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 50rpx;
        margin-left: 20rpx;
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_21 {
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
      .text-wrapper_8 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 50rpx;
        margin-left: 20rpx;
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_22 {
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
    }
  }
  .box_4 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 408rpx;
    width: 702rpx;
    margin: 20rpx 0 0 24rpx;
    .text-wrapper_9 {
      width: 656rpx;
      height: 26rpx;
      margin: 36rpx 0 0 24rpx;
      .text_23 {
        width: 416rpx;
        height: 26rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
      }
      .text_24 {
        width: 72rpx;
        height: 24rpx;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin-top: 2rpx;
      }
    }
    .block_1 {
      width: 654rpx;
      height: 158rpx;
      margin: 72rpx 0 0 22rpx;
      .image-text_2 {
        width: 472rpx;
        height: 158rpx;
        .image_3 {
          width: 156rpx;
          height: 156rpx;
        }
        .text-group_2 {
          width: 300rpx;
          height: 158rpx;
          .text_25 {
            width: 180rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 30rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 30rpx;
          }
          .text_26 {
            width: 180rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 30rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 30rpx;
            margin: -28rpx 0 0 4rpx;
          }
          .text_27 {
            width: 136rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 20rpx 0 0 4rpx;
          }
          .text_28 {
            width: 296rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 14rpx 0 0 4rpx;
          }
          .text-wrapper_10 {
            width: 178rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 30rpx;
            margin: 14rpx 0 0 4rpx;
            .text_29 {
              width: 178rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_30 {
              width: 178rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_31 {
              width: 178rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 30rpx;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 30rpx;
            }
          }
        }
      }
      .text_32 {
        width: 26rpx;
        height: 28rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 28rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 28rpx;
        margin-top: 2rpx;
      }
    }
    .block_2 {
      width: 604rpx;
      height: 50rpx;
      margin: 34rpx 0 32rpx 78rpx;
      .text-wrapper_11 {
        border-radius: 4px;
        height: 50rpx;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_33 {
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
      .text-wrapper_12 {
        border-radius: 4px;
        height: 50rpx;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 20rpx;
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_34 {
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
      .text-wrapper_13 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 50rpx;
        margin-left: 20rpx;
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_35 {
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
      .text-wrapper_14 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 50rpx;
        margin-left: 20rpx;
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_36 {
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
    }
  }
  .box_5 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 408rpx;
    width: 702rpx;
    margin: 20rpx 0 80rpx 24rpx;
    .text-wrapper_15 {
      width: 656rpx;
      height: 26rpx;
      margin: 36rpx 0 0 24rpx;
      .text_37 {
        width: 416rpx;
        height: 26rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
      }
      .text_38 {
        width: 72rpx;
        height: 24rpx;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin-top: 2rpx;
      }
    }
    .group_6 {
      width: 654rpx;
      height: 158rpx;
      margin: 72rpx 0 0 22rpx;
      .image-text_3 {
        width: 472rpx;
        height: 158rpx;
        .image_4 {
          width: 156rpx;
          height: 156rpx;
        }
        .text-group_3 {
          width: 300rpx;
          height: 158rpx;
          .text_39 {
            width: 180rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 30rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 30rpx;
          }
          .text_40 {
            width: 180rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 30rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 30rpx;
            margin: -28rpx 0 0 4rpx;
          }
          .text_41 {
            width: 136rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 20rpx 0 0 4rpx;
          }
          .text_42 {
            width: 296rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 14rpx 0 0 4rpx;
          }
          .text-wrapper_16 {
            width: 178rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 30rpx;
            margin: 14rpx 0 0 4rpx;
            .text_43 {
              width: 178rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_44 {
              width: 178rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_45 {
              width: 178rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 30rpx;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 30rpx;
            }
          }
        }
      }
      .text_46 {
        width: 26rpx;
        height: 28rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 28rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 28rpx;
        margin-top: 2rpx;
      }
    }
    .group_7 {
      width: 604rpx;
      height: 50rpx;
      margin: 34rpx 0 32rpx 78rpx;
      .text-wrapper_17 {
        border-radius: 4px;
        height: 50rpx;
        border: 1px solid rgba(153, 153, 153, 1);
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_47 {
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
      .text-wrapper_18 {
        border-radius: 4px;
        height: 50rpx;
        border: 1px solid rgba(153, 153, 153, 1);
        margin-left: 20rpx;
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_48 {
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
      .text-wrapper_19 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 50rpx;
        margin-left: 20rpx;
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_49 {
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
      .text-wrapper_20 {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 4px;
        height: 50rpx;
        margin-left: 20rpx;
        width: 136rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .text_50 {
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
    }
  }
}

// 订单卡片样式
.order-card {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 10px;
  margin-bottom: 20rpx;
  //padding: 36rpx 24rpx 20rpx 24rpx;
  width: 702rpx;
  margin-left: 24rpx;
  margin-right: 24rpx;

  &:last-child {
    margin-bottom: 80rpx;
  }

  // 重置内部元素的margin，因为现在卡片有了padding
  .text-wrapper_3 {
    margin: 0 0 20rpx 0;
  }

  .group_4 {
    margin: 0 0 20rpx 0;
  }

  .group_5 {
    margin: 0;
  }
}

// 状态切换激活样式
.text-wrapper_1 {
  .text_3, .text_4, .text_5, .text_6, .text_7 {
    transition: color 0.3s ease;
    cursor: pointer;

    &.active {
      color: rgba(11, 206, 148, 1) !important;
      font-weight: 500;
    }

    &:hover {
      color: rgba(11, 206, 148, 0.8);
    }
  }
}

// 按钮布局工具类
.justify-center {
  justify-content: center !important;
}

.justify-between {
  justify-content: space-between !important;
}

.justify-end {
  justify-content: flex-end !important;
  gap: 20rpx; // 按钮之间的间距
}
